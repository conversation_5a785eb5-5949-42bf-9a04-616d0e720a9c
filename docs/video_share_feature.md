# 视频分享功能实现文档

## 功能概述

为视频导出功能增加了分享和取消分享的功能，用户可以将已完成的视频生成分享码进行分享，也可以取消分享。同时提供了分页查询所有已分享视频记录的接口，以及根据分享码查询视频详情的接口。

## 数据库变更

### 表结构修改

在 `ai_video_render_export` 表中新增了以下字段：

- `share_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '分享状态(0-未分享,1-已分享)'
- `share_code` varchar(32) DEFAULT NULL COMMENT '分享码'
- `share_time` datetime DEFAULT NULL COMMENT '分享时间'

### 索引添加

- `idx_share_code` (UNIQUE): 分享码唯一索引，用于根据分享码快速查询
- `idx_share_status_time`: 复合索引，用于查询已分享的视频并按分享时间排序

### 数据库迁移脚本

创建了迁移脚本 `sql/migration/add_share_fields_to_video_render_export.sql` 用于为现有表添加新字段。

## 代码实现

### 1. 实体类修改

**AiVideoRenderExportPo.java**
- 新增 `shareStatus`、`shareCode`、`shareTime` 字段

**VideoRenderExportRes.java**
- 新增分享相关字段和 `getShareStatusDesc()` 方法

### 2. 工具类

**ShareCodeGenerator.java**
- 新增分享码生成器，生成8位随机字符串作为分享码
- 支持唯一性检查和重试机制
- 提供UUID后备方案

### 3. 数据访问层

**AiVideoRenderExportMapper.java**
- 新增 `selectByShareCode()` 方法用于根据分享码查询记录

### 4. 服务层

**VideoRenderExportService.java**
- 新增 `shareVideo()` - 分享视频
- 新增 `unshareVideo()` - 取消分享视频
- 新增 `getSharedVideos()` - 分页查询已分享视频
- 新增 `getVideoByShareCode()` - 根据分享码查询视频

**VideoRenderExportServiceImpl.java**
- 实现上述接口方法
- 修改 `convertToRes()` 方法支持分享状态转换
- 在 `submitRenderExport()` 中初始化分享状态为未分享

### 5. 控制器层

**VideoRenderExportController.java**
- `POST /agent/video-render/share/{taskId}` - 分享视频接口
- `DELETE /agent/video-render/share/{taskId}` - 取消分享接口
- `POST /agent/video-render/shared/list` - 分页查询已分享视频接口
- `GET /agent/video-render/shared/{shareCode}` - 根据分享码查询视频接口

### 6. 请求响应类

**SharedVideoPageReq.java**
- 已分享视频分页查询请求类

## API 接口说明

### 1. 分享视频
```
POST /agent/video-render/share/{taskId}
```
- 功能：将已完成的视频设置为分享状态，生成分享码
- 参数：taskId (路径参数) - 视频任务ID
- 返回：分享码字符串
- 权限：只能分享自己的视频
- 限制：只有渲染完成(status=2)的视频才能分享

### 2. 取消分享视频
```
DELETE /agent/video-render/share/{taskId}
```
- 功能：取消视频分享，清除分享码和分享时间
- 参数：taskId (路径参数) - 视频任务ID
- 返回：操作成功响应
- 权限：只能操作自己的视频

### 3. 分页查询已分享视频
```
POST /agent/video-render/shared/list
```
- 功能：分页查询所有已分享的视频记录
- 参数：SharedVideoPageReq (请求体)
  - pageNum: 页码，默认1
  - pageSize: 每页大小，默认10
- 返回：分页结果，包含视频列表和分页信息
- 排序：按分享时间倒序

### 4. 根据分享码查询视频
```
GET /agent/video-render/shared/{shareCode}
```
- 功能：通过分享码获取视频详情
- 参数：shareCode (路径参数) - 分享码
- 返回：视频详情信息
- 限制：只能查询已分享且渲染完成的视频

## 业务逻辑

### 分享逻辑
1. 验证视频存在且属于当前用户
2. 验证视频状态为已完成(status=2)
3. 如果已经分享过，直接返回现有分享码
4. 生成唯一分享码
5. 更新分享状态、分享码和分享时间

### 取消分享逻辑
1. 验证视频存在且属于当前用户
2. 清除分享状态、分享码和分享时间

### 查询已分享视频逻辑
1. 查询条件：share_status=1, del_flag=0, status=2, share_code IS NOT NULL
2. 按分享时间倒序排列
3. 支持分页查询

### 根据分享码查询逻辑
1. 根据分享码查询记录
2. 验证分享状态和视频状态
3. 返回视频详情（不需要权限验证，公开访问）

## 安全考虑

1. **权限控制**：分享和取消分享操作需要验证用户权限
2. **分享码唯一性**：使用唯一索引确保分享码不重复
3. **状态验证**：只有已完成的视频才能分享
4. **公开访问**：根据分享码查询视频不需要登录，实现公开分享

## 测试建议

1. 测试分享已完成的视频
2. 测试分享未完成的视频（应该失败）
3. 测试重复分享同一视频
4. 测试取消分享
5. 测试分页查询已分享视频
6. 测试根据有效/无效分享码查询视频
7. 测试权限控制（操作他人视频应该失败）
