package com.wlink.agent.controller;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wlink.agent.common.dto.PageRes;
import com.wlink.agent.model.req.SharedVideoPageReq;
import com.wlink.agent.model.req.VideoRenderCallbackReq;
import com.wlink.agent.model.req.VideoRenderExportReq;
import com.wlink.agent.model.req.VideoShareToggleReq;
import com.wlink.agent.model.res.VideoRenderExportRes;
import com.wlink.agent.service.VideoRenderExportService;
import com.wlink.agent.utils.UserContext;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 视频渲染导出控制器
 */
@Tag(name = "视频渲染导出", description = "视频渲染导出相关接口")
@Slf4j
@RestController
@RequestMapping("/agent/video-render")
@RequiredArgsConstructor
public class VideoRenderExportController {

    private final VideoRenderExportService videoRenderExportService;

    /**
     * 提交视频渲染导出任务
     *
     * @param req 渲染导出请求
     * @return 渲染任务ID
     */
    @PostMapping("/export")
    @Operation(summary = "提交视频渲染导出任务")
    public SingleResponse<Long> submitRenderExport(@Valid @RequestBody VideoRenderExportReq req) {
        log.info("提交视频渲染导出任务: canvasId={}, resolution={}, showSubtitle={}",
                req.getCanvasId(), req.getResolution(), req.getShowSubtitle());
        Long taskId = videoRenderExportService.submitRenderExport(req);
        return SingleResponse.of(taskId);
    }

    /**
     * 获取用户的渲染导出任务列表
     *
     * @param canvasId 画布ID (可选)
     * @return 渲染任务列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取用户的渲染导出任务列表")
    public MultiResponse<VideoRenderExportRes> getUserRenderExports(
            @Parameter(description = "画布ID (可选)", required = false)
            @RequestParam(value = "canvasId", required = false) Long canvasId) {
        String userId = UserContext.getUser().getUserId();
        log.info("获取用户渲染导出任务列表: userId={}, canvasId={}", userId, canvasId);
        List<VideoRenderExportRes> exportList = videoRenderExportService.getUserRenderExports(userId, canvasId);
        return MultiResponse.of(exportList);
    }

    /**
     * 获取渲染导出任务详情
     *
     * @param taskId 任务ID
     * @return 渲染任务详情
     */
    @GetMapping("/{taskId}")
    @Operation(summary = "获取渲染导出任务详情")
    public SingleResponse<VideoRenderExportRes> getRenderExportDetail(
            @Parameter(description = "任务ID", required = true)
            @PathVariable("taskId") Long taskId) {
        log.info("获取渲染导出任务详情: taskId={}", taskId);
        VideoRenderExportRes detail = videoRenderExportService.getRenderExportDetail(taskId);
        return SingleResponse.of(detail);
    }


    /**
     * 视频渲染回调接口
     * 供Python渲染服务调用，用于更新渲染任务状态
     *
     * @param req 回调请求
     * @return 处理结果
     */
    @PostMapping("/callback")
    @Operation(summary = "视频渲染回调接口", description = "供Python渲染服务调用，用于更新渲染任务状态")
    public ResponseEntity<String> handleRenderCallback(@Valid @RequestBody VideoRenderCallbackReq req) {
        log.info("收到视频渲染回调: taskId={}, status={}, videoUrl={}, errorMessage={}",
                req.getTaskId(), req.getStatus(), req.getVideoUrl(), req.getErrorMessage());

        // 异步处理回调，立即返回成功响应
        videoRenderExportService.handleRenderCallbackAsync(
                req.getTaskId(),
                req.getStatus(),
                req.getErrorMessage(),
                req.getVideoUrl(),
                req.getVideoDuration()
        );

        // 立即返回成功响应，不等待处理结果
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", 200);
        jsonObject.put("message", "callback received and processing");

        log.info("视频渲染回调已接收，正在异步处理: taskId={}", req.getTaskId());
        return ResponseEntity.ok(JSON.toJSONString(jsonObject));
    }

    /**
     * 删除视频渲染导出记录
     * 只能删除状态为成功(2)和失败(3)的记录
     *
     * @param id 任务ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{taskId}")
    @Operation(summary = "删除视频渲染导出记录", description = "只能删除已完成或失败的渲染记录")
    public Response deleteRenderExport(
            @Parameter(description = "任务ID", required = true)
            @PathVariable("id") Long id) {
        log.info("删除视频渲染导出记录: taskId={}", id);
        videoRenderExportService.deleteRenderExport(id);
        return Response.buildSuccess();
    }

    /**
     * 分享/取消分享视频
     *
     * @param taskId 任务ID
     * @param share 分享状态：true-分享，false-取消分享
     * @return 分享码（分享时）或操作成功响应（取消分享时）
     */
    @PutMapping("/share/{taskId}")
    @Operation(summary = "分享/取消分享视频", description = "分享或取消分享视频，share=true时分享并返回分享码，share=false时取消分享")
    public SingleResponse<String> toggleVideoShare(
            @Parameter(description = "任务ID", required = true)
            @PathVariable("taskId") Long taskId,
            @Parameter(description = "分享状态：true-分享，false-取消分享", required = true)
            @RequestParam("share") Boolean share) {

        if (Boolean.TRUE.equals(share)) {
            log.info("分享视频: taskId={}", taskId);
            String shareCode = videoRenderExportService.shareVideo(taskId);
            return SingleResponse.of(shareCode);
        } else {
            log.info("取消分享视频: taskId={}", taskId);
            videoRenderExportService.unshareVideo(taskId);
            return SingleResponse.of(null);
        }
    }

    /**
     * 分页查询已分享的视频记录
     *
     * @param req 分页查询请求
     * @return 分页结果
     */
    @PostMapping("/shared/list")
    @Operation(summary = "分页查询已分享的视频记录", description = "查询所有已分享的视频记录")
    public PageResponse<VideoRenderExportRes> getSharedVideos(@Valid @RequestBody SharedVideoPageReq req) {
        log.info("分页查询已分享视频: pageNum={}, pageSize={}", req.getPageNum(), req.getPageSize());
        PageRes<VideoRenderExportRes> pageResult = videoRenderExportService.getSharedVideos(req.getPageNum(), req.getPageSize());
        return PageResponse.of(pageResult.getList(), (int) pageResult.getTotal(), req.getPageSize(), req.getPageNum());
    }

    /**
     * 根据分享码查询视频导出记录
     *
     * @param shareCode 分享码
     * @return 视频导出记录
     */
    @GetMapping("/shared/{shareCode}")
    @Operation(summary = "根据分享码查询视频", description = "通过分享码获取视频详情")
    public SingleResponse<VideoRenderExportRes> getVideoByShareCode(
            @Parameter(description = "分享码", required = true)
            @PathVariable("shareCode") String shareCode) {
        log.info("根据分享码查询视频: shareCode={}", shareCode);
        VideoRenderExportRes video = videoRenderExportService.getVideoByShareCode(shareCode);
        return SingleResponse.of(video);
    }
}
