import com.wlink.agent.model.req.TtsGenerateReq;
import com.wlink.agent.model.req.ShotAudioCreateReq;

/**
 * Emotion 参数验证功能演示
 * 
 * 演示当传入不符合条件的 emotion 值时，会自动重置为 null，而不是报错
 */
public class EmotionValidationDemo {
    
    public static void main(String[] args) {
        System.out.println("=== Emotion 参数验证演示 ===\n");
        
        // 演示 TtsGenerateReq
        System.out.println("1. TtsGenerateReq 演示:");
        demonstrateTtsGenerateReq();
        
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        // 演示 ShotAudioCreateReq
        System.out.println("2. ShotAudioCreateReq 演示:");
        demonstrateShotAudioCreateReq();
    }
    
    private static void demonstrateTtsGenerateReq() {
        TtsGenerateReq req = new TtsGenerateReq();
        
        // 测试有效值
        System.out.println("有效值测试:");
        String[] validEmotions = {"happy", "sad", "angry", "fearful", "disgusted", "surprised", "neutral"};
        for (String emotion : validEmotions) {
            req.setEmotion(emotion);
            System.out.printf("  设置: %-10s -> 结果: %s%n", emotion, req.getEmotion());
        }
        
        System.out.println("\n无效值测试 (会被重置为 null):");
        String[] invalidEmotions = {"invalid", "Happy", "SAD", "joyful", "excited", "", " ", "123"};
        for (String emotion : invalidEmotions) {
            req.setEmotion(emotion);
            System.out.printf("  设置: %-10s -> 结果: %s%n", 
                emotion.isEmpty() ? "\"\"" : emotion.equals(" ") ? "\" \"" : emotion, 
                req.getEmotion());
        }
        
        System.out.println("\nnull 值测试:");
        req.setEmotion(null);
        System.out.printf("  设置: %-10s -> 结果: %s%n", "null", req.getEmotion());
    }
    
    private static void demonstrateShotAudioCreateReq() {
        ShotAudioCreateReq req = new ShotAudioCreateReq();
        
        // 测试有效值
        System.out.println("有效值测试:");
        String[] validEmotions = {"happy", "sad", "angry", "fearful", "disgusted", "surprised", "neutral"};
        for (String emotion : validEmotions) {
            req.setEmotion(emotion);
            System.out.printf("  设置: %-10s -> 结果: %s%n", emotion, req.getEmotion());
        }
        
        System.out.println("\n无效值测试 (会被重置为 null):");
        String[] invalidEmotions = {"invalid", "Happy", "SAD", "joyful", "excited", "", " ", "123"};
        for (String emotion : invalidEmotions) {
            req.setEmotion(emotion);
            System.out.printf("  设置: %-10s -> 结果: %s%n", 
                emotion.isEmpty() ? "\"\"" : emotion.equals(" ") ? "\" \"" : emotion, 
                req.getEmotion());
        }
        
        System.out.println("\nnull 值测试:");
        req.setEmotion(null);
        System.out.printf("  设置: %-10s -> 结果: %s%n", "null", req.getEmotion());
    }
}
