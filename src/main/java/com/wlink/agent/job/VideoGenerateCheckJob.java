//package com.wlink.agent.job;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.wlink.agent.dao.mapper.AgentVideoRecordMapper;
//import com.wlink.agent.dao.po.AgentVideoRecordPo;
//import com.wlink.agent.service.VideoGenerateService;
//import com.xxl.job.core.biz.model.ReturnT;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.List;
//
///**
// * 视频生成结果检查任务
// */
//@Slf4j
//@Component
//public class VideoGenerateCheckJob {
//
//    @Resource
//    private AgentVideoRecordMapper videoRecordMapper;
//
//    @Resource
//    private VideoGenerateService videoGenerateService;
//
//    /**
//     * 检查视频生成结果
//     * 每1分钟执行一次
//     */
//    @XxlJob("videoGenerateCheckJobHandler")
//    public ReturnT<String> execute(String param) {
//        log.info("Start checking video generate results...");
//        try {
//            // 查询状态为生成中的记录
//            List<AgentVideoRecordPo> records = videoRecordMapper.selectList(
//                new LambdaQueryWrapper<AgentVideoRecordPo>()
//                    .eq(AgentVideoRecordPo::getStatus, 1)
//                    .orderByAsc(AgentVideoRecordPo::getCreateTime)
//                    .last("limit 10") // 每次处理10条
//            );
//
//            if (records.isEmpty()) {
//                log.info("No pending video generate records found");
//                return ReturnT.SUCCESS;
//            }
//            // 逐个处理记录
//            for (AgentVideoRecordPo record : records) {
//                try {
//                    log.info("Checking video generate result for taskId: {}", record.getTaskId());
//                    videoGenerateService.getGenerateResultWithRecord(record.getTaskId());
//                } catch (Exception e) {
//                    // 单条记录处理失败不影响其他记录的处理
//                    log.error("Failed to check video generate result for taskId: {}", record.getTaskId(), e);
//                }
//            }
//            return ReturnT.SUCCESS;
//        } catch (Exception e) {
//            log.error("Video generate check job failed", e);
//            return ReturnT.FAIL;
//        }
//    }
//}