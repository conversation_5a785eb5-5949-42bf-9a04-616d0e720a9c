-- 为视频渲染导出表添加分享相关字段
-- 执行时间: 2024-01-01

-- 添加分享状态字段
ALTER TABLE `ai_video_render_export` 
ADD COLUMN `share_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '分享状态(0-未分享,1-已分享)' AFTER `complete_time`;

-- 添加分享码字段
ALTER TABLE `ai_video_render_export` 
ADD COLUMN `share_code` varchar(32) DEFAULT NULL COMMENT '分享码' AFTER `share_status`;

-- 添加分享时间字段
ALTER TABLE `ai_video_render_export` 
ADD COLUMN `share_time` datetime DEFAULT NULL COMMENT '分享时间' AFTER `share_code`;

-- 添加分享码唯一索引
ALTER TABLE `ai_video_render_export` 
ADD UNIQUE KEY `idx_share_code` (`share_code`);

-- 添加分享状态和时间的复合索引
ALTER TABLE `ai_video_render_export` 
ADD KEY `idx_share_status_time` (`share_status`, `share_time`);
