package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 画布视频资源响应
 */
@Data
@Schema(description = "画布视频资源响应")
public class AiCanvasVideoRes {
    
    /**
     * 视频资源ID
     */
    @Schema(description = "视频资源ID")
    private Long id;
    
    /**
     * 画布ID
     */
    @Schema(description = "画布ID")
    private Long canvasId;
    
    /**
     * 分镜编码
     */
    @Schema(description = "分镜编码")
    private String shotCode;
    
    /**
     * 视频URL
     */
    @Schema(description = "视频URL")
    private String videoUrl;
    
    /**
     * 视频生成提示词
     */
    @Schema(description = "视频生成提示词")
    private String videoPrompt;
    
    /**
     * 视频描述
     */
    @Schema(description = "视频描述")
    private String videoDesc;
    
    /**
     * 视频时长(毫秒)
     */
    @Schema(description = "视频时长(毫秒)")
    private Integer videoDuration;
    
    /**
     * 视频宽高比
     */
    @Schema(description = "视频宽高比")
    private String videoAspectRatio;
    
    /**
     * 视频状态
     */
    @Schema(description = "视频状态")
    private String videoStatus;
    
    /**
     * 开始帧图片URL
     */
    @Schema(description = "开始帧图片URL")
    private String startFrameImage;
    
    /**
     * 结束帧图片URL
     */
    @Schema(description = "结束帧图片URL")
    private String endFrameImage;
    
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
} 