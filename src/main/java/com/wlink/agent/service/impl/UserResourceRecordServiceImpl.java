package com.wlink.agent.service.impl;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wlink.agent.dao.dto.UserImageRecordDTO;
import com.wlink.agent.dao.mapper.UserImageRecordMapper;
import com.wlink.agent.dao.mapper.AiVideoGenerationMapper;
import com.wlink.agent.dao.mapper.AiVideoRenderExportMapper;
import com.wlink.agent.dao.mapper.AiCanvasMapper;
import com.wlink.agent.dao.po.AiVideoGenerationPo;
import com.wlink.agent.dao.po.AiVideoRenderExportPo;
import com.wlink.agent.dao.po.AiCanvasPo;
import com.wlink.agent.model.req.UserImageQueryReq;
import com.wlink.agent.model.req.UserVideoQueryReq;
import com.wlink.agent.model.res.UserImageRecordRes;
import com.wlink.agent.model.res.UserVideoRecordRes;
import com.wlink.agent.service.UserResourceRecordService;
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户图片记录服务实现
 */
@Slf4j
@Service
public class UserResourceRecordServiceImpl implements UserResourceRecordService {

    @Resource
    private UserImageRecordMapper userImageRecordMapper;

    @Resource
    private AiVideoGenerationMapper aiVideoGenerationMapper;

    @Resource
    private AiVideoRenderExportMapper aiVideoRenderExportMapper;
    
    @Resource
    private AiCanvasMapper aiCanvasMapper;
    
    @Override
    public PageResponse<UserImageRecordRes> queryUserImageRecords(UserImageQueryReq req) {
        // 计算分页参数
        int offset = (req.getPageNum() - 1) * req.getPageSize();
        int limit = req.getPageSize();

        // 查询总数
        long total = userImageRecordMapper.countUserImageRecords(req.getUserId(), req.getType(), req.getSessionId());

        // 查询列表
        List<UserImageRecordDTO> records = userImageRecordMapper.findUserImageRecords(
                req.getUserId(), req.getType(), req.getSessionId(), offset, limit);
        
        // 转换结果
        List<UserImageRecordRes> resultList = records.stream()
                .map(this::convertToRes)
                .toList();

        // 构建分页结果
        return PageResponse.of(resultList, (int) total, req.getPageSize(), req.getPageNum());
    }

    /**
     * 将DTO转换为UserImageRecordRes对象
     */
    private UserImageRecordRes convertToRes(UserImageRecordDTO dto) {
        UserImageRecordRes res = new UserImageRecordRes();
        
        res.setId(dto.getId());
        res.setSessionId(dto.getSessionId());
        res.setUserId(dto.getUserId());
        res.setTaskType(dto.getTaskType());
        res.setContentType(dto.getContentType());
        res.setContentId(dto.getContentId());
        res.setImageUrl(MediaUrlPrefixUtil.getMediaUrl(dto.getImageUrl()));
        res.setCreateTime(dto.getCreateTime());
        res.setIsFavorite(dto.getIsFavorite());
        res.setFavoriteCode(dto.getFavoriteCode());
        res.setFavoriteTime(dto.getFavoriteTime());
        // 从requestParams中提取prompt和图片比例
        String requestParams = dto.getRequestParams();
        if (!StringUtils.isEmpty(requestParams)) {
            try {
                JSONObject params = JSON.parseObject(requestParams);
                
                // 提取prompt
                if (params.containsKey("prompt")) {
                    res.setPrompt(params.getString("prompt"));
                }
                // 提取图片比例
                if (params.containsKey("aspect_ratio")) {
                    res.setAspectRatio(params.getString("aspect_ratio"));
                    //"size":"2048x1152"
                } else if (params.containsKey("size")) {
                        String size = params.getString("size");
                        String[] sizeArray = size.split("x");
                        int width = Integer.parseInt(sizeArray[0]);
                        int height = Integer.parseInt(sizeArray[1]);
                        res.setAspectRatio(calculateAspectRatio(width, height));
             }
            } catch (Exception e) {
                log.error("解析requestParams失败", e);
            }
        }
        return res;
    }

    /**
     * 计算宽高比，格式为"9:16"
     *
     * @param width 宽度
     * @param height 高度
     * @return 格式化的宽高比字符串
     */
    private String calculateAspectRatio(int width, int height) {
        if (width <= 0 || height <= 0) {
            return "1:1"; // 默认正方形
        }

        // 计算最大公约数
        int gcd = gcd(width, height);

        // 使用最大公约数计算最简比例
        int aspectWidth = width / gcd;
        int aspectHeight = height / gcd;
        if (aspectHeight == 3 || aspectHeight == 7){
            aspectWidth = 3 * aspectWidth;
            aspectHeight = 3 * aspectHeight;
        }
        return aspectWidth + ":" + aspectHeight;
    }

    /**
     * 计算最大公约数的辅助方法
     */
    private int gcd(int a, int b) {
        return b == 0 ? a : gcd(b, a % b);
    }

    @Override
    public PageResponse<UserVideoRecordRes> queryUserVideoRecords(UserVideoQueryReq req) {
        // 如果type为1，按照现有逻辑查询ai_video_generation表
        if (req.getVideoType() == null || req.getVideoType() == 1) {
            return queryAiVideoGenerationRecords(req);
        } 
        // 如果type为2，查询ai_video_render_export表
        else if (req.getVideoType() == 2) {
            return queryAiVideoRenderExportRecords(req);
        }
        
        // 默认情况，返回空结果
        return PageResponse.of(new ArrayList<>(), 0, req.getPageSize(), req.getPageNum());
    }

    private PageResponse<UserVideoRecordRes> queryAiVideoGenerationRecords(UserVideoQueryReq req) {
        String userId = req.getUserId();
        int pageNum = req.getPageNum();
        int pageSize = req.getPageSize();
        int offset = (pageNum - 1) * pageSize;
        
        // 查询总数
        long total = aiVideoGenerationMapper.countUserVideoRecordsByStatus(userId, 2);
        
        // 查询列表
        List<AiVideoGenerationPo> records = aiVideoGenerationMapper.findUserVideoRecordsByStatus(
                userId, 2, offset, pageSize);
        
        // 转换结果
        List<UserVideoRecordRes> resultList = records.stream()
                .map(this::convertToVideoRes)
                .toList();
        
        return PageResponse.of(resultList, (int) total, req.getPageSize(), req.getPageNum());
    }
    
    private PageResponse<UserVideoRecordRes> queryAiVideoRenderExportRecords(UserVideoQueryReq req) {
        String userId = req.getUserId();
        int pageNum = req.getPageNum();
        int pageSize = req.getPageSize();
        
        // 使用MyBatis Plus分页查询
        Page<AiVideoRenderExportPo> page = new Page<>(pageNum, pageSize);
        Page<AiVideoRenderExportPo> resultPage = aiVideoRenderExportMapper.selectPage(
            page,
            new LambdaQueryWrapper<AiVideoRenderExportPo>()
                .eq(AiVideoRenderExportPo::getUserId, userId)
                .eq(AiVideoRenderExportPo::getStatus, 2)
                .eq(AiVideoRenderExportPo::getDelFlag, 0)
                .orderByDesc(AiVideoRenderExportPo::getCreateTime)
        );
        
        // 转换结果
        List<UserVideoRecordRes> resultList = resultPage.getRecords().stream()
                .map(this::convertToVideoResFromRenderExport)
                .toList();
        
        return PageResponse.of(resultList, 
                             (int) resultPage.getTotal(), 
                             req.getPageSize(), 
                             req.getPageNum());
    }

    private UserVideoRecordRes convertToVideoRes(AiVideoGenerationPo po) {
        if (po == null) return null;
        return UserVideoRecordRes.builder()
                .id(po.getId())
                .userId(po.getUserId())
                .shotId(po.getShotId())
                .generateTaskId(po.getGenerateTaskId())
                .model(po.getModel())
                .prompt(po.getPrompt())
                .firstFrameImage(MediaUrlPrefixUtil.getMediaUrl(po.getFirstFrameImage()))
                .lastFrameImage(MediaUrlPrefixUtil.getMediaUrl(po.getLastFrameImage()))
                .resolution(po.getResolution())
                .ratio(po.getRatio())
                .duration(po.getDuration() * 1000)
                .fps(po.getFps())
                .status(po.getStatus())
                .videoUrl(MediaUrlPrefixUtil.getMediaUrl(po.getVideoUrl()))
                .taskInfo(po.getTaskInfo())
                .errorMessage(po.getErrorMessage())
                .queuePosition(po.getQueuePosition())
                .startTime(po.getStartTime())
                .completeTime(po.getCompleteTime())
                .createTime(po.getCreateTime())
                .updateTime(po.getUpdateTime())
                .build();
    }
    
    private UserVideoRecordRes convertToVideoResFromRenderExport(AiVideoRenderExportPo po) {
        if (po == null) return null;

        UserVideoRecordRes res = new UserVideoRecordRes();
        res.setId(po.getId());
        res.setUserId(po.getUserId());
        res.setResolution(po.getResolution());
        res.setRatio(po.getRatio());
        res.setStatus(po.getStatus());
        res.setVideoUrl(MediaUrlPrefixUtil.getMediaUrl(po.getVideoUrl()));
        res.setStartTime(po.getStartTime());
        res.setCompleteTime(po.getCompleteTime());
        res.setCreateTime(po.getCreateTime());
        res.setUpdateTime(po.getUpdateTime());

        // 设置分享相关字段
        res.setShareStatus(po.getShareStatus() != null ? po.getShareStatus() : 0);
        res.setShareCode(po.getShareCode());
        res.setShareTime(po.getShareTime());

        // 设置画布名称作为prompt
        if (po.getCanvasId() != null) {
            AiCanvasPo canvasPo = aiCanvasMapper.selectById(po.getCanvasId());
            if (canvasPo != null) {
                res.setPrompt(canvasPo.getCanvasName());
                res.setFirstFrameImage(MediaUrlPrefixUtil.getMediaUrl(canvasPo.getCoverImage()));
            }
        }

        return res;
    }
} 