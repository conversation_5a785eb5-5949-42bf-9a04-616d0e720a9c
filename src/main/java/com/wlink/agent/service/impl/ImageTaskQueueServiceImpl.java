package com.wlink.agent.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.client.CompletionApiClient;
import com.wlink.agent.client.FalAiFluxClient;
import com.wlink.agent.client.VolcengineImageApiClient;
import com.wlink.agent.client.model.doubao.DoubaoImageEditRequest;
import com.wlink.agent.client.model.doubao.DoubaoImageGenerationRequest;
import com.wlink.agent.client.model.volcengine.ImageGenerateRes;
import com.wlink.agent.client.model.volcengine.VolcengineCharacterRetentionRequest;
import com.wlink.agent.client.model.volcengine.VolcengineImageRequest;
import com.wlink.agent.client.model.volcengine.VolcengineSeedEditRequest;
import com.wlink.agent.config.ExternalApiConfig;
import com.wlink.agent.dao.mapper.AiCreationContentMapper;
import com.wlink.agent.dao.mapper.AiCreationSessionMapper;
import com.wlink.agent.dao.mapper.AiImageModifyRecordMapper;
import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.mapper.AiPointTransactionsMapper;
import com.wlink.agent.dao.mapper.AiShotMapper;
import com.wlink.agent.dao.mapper.AiUsersMapper;
import com.wlink.agent.dao.po.AiCreationContentPo;
import com.wlink.agent.dao.po.AiCreationSessionPo;
import com.wlink.agent.dao.po.AiCreationVisualRecordPo;
import com.wlink.agent.dao.po.AiImageModifyRecordPo;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.dao.po.AiPointTransactionsPo;
import com.wlink.agent.dao.po.AiShotPo;
import com.wlink.agent.dao.po.AiUsersPo;
import com.wlink.agent.dao.po.VendorAccountPo;
import com.wlink.agent.enums.TaskStatus;
import com.wlink.agent.enums.TaskType;
import com.wlink.agent.model.dto.SimpleUserInfo;
import com.wlink.agent.model.req.RoleSaveReq;
import com.wlink.agent.model.req.ShotSaveReq;
import com.wlink.agent.model.req.VisualSaveReq;
import com.wlink.agent.mq.ImageTaskMQHandler;
import com.wlink.agent.service.AiCreationVisualRecordService;
import com.wlink.agent.service.ImageTaskQueueService;
import com.wlink.agent.service.EventPublishService;
import com.wlink.agent.service.VendorAccountService;
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import com.wlink.agent.utils.OssUtils;
import com.wlink.agent.utils.UserContext;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class ImageTaskQueueServiceImpl implements ImageTaskQueueService {

    @Autowired
    private AiImageTaskQueueMapper imageTaskQueueMapper;

    @Autowired
    private VolcengineImageApiClient volcengineImageApiClient;

    @Autowired
    private ExternalApiConfig externalApiConfig;

    @Autowired
    private CompletionApiClient completionApiClient;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AiCreationSessionMapper aiCreationSessionMapper;

    @Autowired
    private OssUtils ossUtil;

    @Resource
    private AiCreationContentMapper aiCreationContentMapper;

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private AiCreationVisualRecordService visualRecordService;


    @Resource
    private VendorAccountService vendorAccountService;
    @Resource
    ImageTaskMQHandler imageTaskMQHandler;

    @Resource
    private EventPublishService eventPublishService;


    private static final int MAX_RETRY_COUNT = 3;

    // 新增：错误码重试机制相关常量
    private static final int MAX_ERROR_CODE_RETRY_COUNT = 3;
    private static final String ERROR_CODE_RETRY_FORMAT = "AI_COMPLETION_RETRY";

    // 特定错误码集合
    private static final Set<String> SPECIFIC_ERROR_CODES = new HashSet<>(Arrays.asList("50411", "50511", "50412", "50512", "50413"));

    private RAtomicLong globalPositionCounter;
    private static final String SESSION_POSITION_PREFIX = "session-position:";
    private static final String GLOBAL_POSITION_KEY = "global-position-counter";
    private static final long LOCK_WAIT_TIME = 10;
    private static final long LOCK_LEASE_TIME = 5;

    @Value("${spring.profiles.active}")
    String env;
    //safetyTolerance
    @Value("${flux.image.safetyTolerance:5}")
    private String safetyTolerance;


    /**
     * oss路径
     *
     * @param conversationId 会话ID
     * @param storyData      故事数据
     */
    private static final String OSS_PATH = "dify/{env}/{sessionId}/{type}/";
    @Autowired
    private AiImageModifyRecordMapper aiImageModifyRecordMapper;

    @Autowired
    private AiUsersMapper aiUsersMapper;

    @Autowired
    private AiPointTransactionsMapper aiPointTransactionsMapper;
    @Autowired
    private AiShotMapper aiShotMapper;

    @PostConstruct
    public void init() {
        // 初始化全局位置计数器
        globalPositionCounter = redissonClient.getAtomicLong(GLOBAL_POSITION_KEY);

    }

    /**
     * 计算宽高比，格式为"9:16"
     *
     * @param width  宽度
     * @param height 高度
     * @return 格式化的宽高比字符串
     */
    private String calculateAspectRatio(int width, int height) {
        if (width <= 0 || height <= 0) {
            return "1:1"; // 默认正方形
        }

        // 计算最大公约数
        int gcd = gcd(width, height);

        // 使用最大公约数计算最简比例
        int aspectWidth = width / gcd;
        int aspectHeight = height / gcd;
        if (aspectHeight == 3 || aspectHeight == 7) {
            aspectWidth = 3 * aspectWidth;
            aspectHeight = 3 * aspectHeight;
        }
        return aspectWidth + ":" + aspectHeight;
    }

    /**
     * 计算最大公约数的辅助方法
     */
    private int gcd(int a, int b) {
        return b == 0 ? a : gcd(b, a % b);
    }

    @Override
    @Transactional
    public AiImageTaskQueuePo queueGenerateTask(String sessionId, VolcengineImageRequest request, String taskType) {
        try {
            // 获取会话锁，防止并发提交同一会话的任务造成位置计算错误
            RLock sessionLock = redissonClient.getLock("session-lock:" + sessionId);
            if (!sessionLock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS)) {
                throw new BizException("会话锁定中，请稍后重试");
            }
            try {
                AiCreationSessionPo aiCreationSessionPo = aiCreationSessionMapper.selectOne(new LambdaQueryWrapper<AiCreationSessionPo>()
                        .eq(AiCreationSessionPo::getSessionId, sessionId)
                        .last("LIMIT 1"));
                if (aiCreationSessionPo == null) {
                    log.error("会话不存在");
                    throw new BizException("会话不存在");
                }

                // 使用Redisson原子计数器获取全局位置
                int globalPosition = (int) globalPositionCounter.incrementAndGet();

                // 使用Redisson获取会话位置计数器
                RAtomicLong sessionPositionCounter = redissonClient.getAtomicLong(SESSION_POSITION_PREFIX + sessionId);
                int sessionPosition = (int) sessionPositionCounter.incrementAndGet();

                // 生成请求ID
                String requestId = IdUtil.fastSimpleUUID();

                // 获取供应商类型，默认为DOUBAO
                String imageModel = request.getImageModel() != null ? request.getImageModel() : "DOUBAO";

                // 准备请求对象和JSON
                String requestJson;

                // 检查是否需要使用图生图（ImageToImage）而不是文生图（TextToImage）
                boolean hasImageUrls = request.getImageUrls() != null && !request.getImageUrls().isEmpty();

                // 计算宽高比（如果宽高都提供了）


                if ("DOUBAO".equalsIgnoreCase(imageModel)) {
                    // 豆包 API
                    DoubaoImageGenerationRequest doubaoImageGenerationRequest = new DoubaoImageGenerationRequest();
                    doubaoImageGenerationRequest.setModel("doubao-seedream-3-0-t2i-250415");
                    doubaoImageGenerationRequest.setPrompt(request.getPrompt());
                    doubaoImageGenerationRequest.setSeed(request.getSeed());
                    doubaoImageGenerationRequest.setGuidanceScale(Double.valueOf(request.getScale()));
                    doubaoImageGenerationRequest.setSize(request.getWidth() + "x" + request.getHeight());
                    doubaoImageGenerationRequest.setCnPrompt(request.getPrompt());

                    requestJson = objectMapper.writeValueAsString(doubaoImageGenerationRequest);
                } else {
                    String aspectRatio = null;
                    if (request.getWidth() != null && request.getHeight() != null) {
                        aspectRatio = calculateAspectRatio(request.getWidth(), request.getHeight());
                    }
                    // Flux AI API
                    if (hasImageUrls) {
                        // 使用图生图请求
                        FalAiFluxClient.ImageToImageRequest.Builder builder = new FalAiFluxClient.ImageToImageRequest.Builder()
                                .prompt(request.getPrompt());
                        // 设置图片URL（只取第一个，因为Flux只支持单个图片）
                        if (!request.getImageUrls().isEmpty()) {
                            builder.imageUrl(request.getImageUrls().get(0));
                        }
                        builder.cnPrompt(request.getCnPrompt());
                        // 设置其他参数
                        builder.seed(request.getSeed());
                        builder.guidanceScale(request.getScale().doubleValue());
                        builder.aspectRatio(aspectRatio);
                        // 默认只生成一张图
                        builder.numImages(1);
                        builder.syncMode(false);
                        builder.outputFormat("png");
                        builder.safetyTolerance(safetyTolerance);
                        FalAiFluxClient.ImageToImageRequest imageToImageRequest = builder.build();
                        requestJson = objectMapper.writeValueAsString(imageToImageRequest);
                    } else {
                        // 使用文生图请求
                        FalAiFluxClient.TextToImageRequest.Builder builder = new FalAiFluxClient.TextToImageRequest.Builder()
                                .prompt(request.getPrompt());
                        // 设置其他参数
                        builder.seed(request.getSeed());
                        builder.cnPrompt(request.getCnPrompt());
                        builder.guidanceScale(request.getScale().doubleValue());
                        builder.aspectRatio(aspectRatio);
                        // 默认只生成一张图
                        builder.numImages(1);
                        builder.syncMode(false);
                        builder.outputFormat("png");
                        builder.safetyTolerance(safetyTolerance);
                        FalAiFluxClient.TextToImageRequest textToImageRequest = builder.build();
                        requestJson = objectMapper.writeValueAsString(textToImageRequest);
                    }
                }
                // 所有新任务都以PENDING状态保存
                AiImageTaskQueuePo po = com.wlink.agent.dao.po.AiImageTaskQueuePo.builder()
                        .sessionId(sessionId)
                        .userId(aiCreationSessionPo.getUserId())
                        .contentType(request.getType())
                        .contentId(request.getContentId())
                        .taskType(taskType)
                        .requestParams(requestJson)
                        .taskStatus(TaskStatus.PENDING.getValue())
                        .sessionQueuePosition(sessionPosition)
                        .globalQueuePosition(globalPosition)
                        .vendorAccountId(null)
                        .imageModel(imageModel)
                        .build();
                imageTaskQueueMapper.insert(po);

                // 使用RocketMQ处理器发送任务消息
                String mqTaskType;
                if ("DOUBAO".equalsIgnoreCase(imageModel)) {
                    mqTaskType = TaskType.GENERATE_DOUBAO.getValue();
                } else {
                    mqTaskType = TaskType.GENERATE_FLUX.getValue();
                }
                imageTaskMQHandler.sendTaskMessage(String.valueOf(po.getId()), mqTaskType);

                return po;
            } finally {
                // 确保会话锁被释放
                if (sessionLock.isHeldByCurrentThread()) {
                    sessionLock.unlock();
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while acquiring lock", e);
            throw new BizException("线程被中断，任务添加失败");
        } catch (Exception e) {
            log.error("Error queuing generate task", e);
            throw new BizException("Failed to queue generate task: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public AiImageTaskQueuePo queueRetainTask(String sessionId, VolcengineCharacterRetentionRequest request) {
        try {
            // 获取会话锁，防止并发提交同一会话的任务造成位置计算错误
            RLock sessionLock = redissonClient.getLock("session-lock:" + sessionId);
            if (!sessionLock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS)) {
                throw new BizException("会话锁定中，请稍后重试");
            }

            try {
                AiCreationSessionPo aiCreationSessionPo = aiCreationSessionMapper.selectOne(new LambdaQueryWrapper<AiCreationSessionPo>()
                        .eq(AiCreationSessionPo::getSessionId, sessionId)
                        .last("LIMIT 1"));
                if (aiCreationSessionPo == null) {
                    log.error("会话不存在");
                    throw new BizException("会话不存在");
                }
                // 使用Redisson原子计数器获取全局位置
                int globalPosition = (int) globalPositionCounter.incrementAndGet();

                // 使用Redisson获取会话位置计数器
                RAtomicLong sessionPositionCounter = redissonClient.getAtomicLong(SESSION_POSITION_PREFIX + sessionId);
                int sessionPosition = (int) sessionPositionCounter.incrementAndGet();


                String requestJson = objectMapper.writeValueAsString(request);

                // 所有新任务都以PENDING状态保存
                AiImageTaskQueuePo po = com.wlink.agent.dao.po.AiImageTaskQueuePo.builder()
                        .sessionId(sessionId)
                        .taskType(TaskType.RETAIN.getValue())
                        .requestParams(requestJson)
                        .contentType(request.getType())
                        .contentId(request.getContentId())
                        .taskStatus(TaskStatus.PENDING.getValue())
                        .sessionQueuePosition(sessionPosition)
                        .globalQueuePosition(globalPosition)
                        .vendorAccountId(null)
                        .userId(aiCreationSessionPo.getUserId())
                        .build();
                imageTaskQueueMapper.insert(po);

                // 使用RocketMQ处理器发送任务消息
                imageTaskMQHandler.sendTaskMessage(String.valueOf(po.getId()), TaskType.GENERATE_DOUBAO.getValue());

                return po;
            } finally {
                // 确保会话锁被释放
                if (sessionLock.isHeldByCurrentThread()) {
                    sessionLock.unlock();
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while acquiring lock", e);
            throw new BizException("线程被中断，任务添加失败");
        } catch (Exception e) {
            log.error("Error queuing retain task", e);
            throw new BizException("Failed to queue retain task: " + e.getMessage());
        }
    }


    @Override
    @Transactional
    public AiImageTaskQueuePo queueEditTask(String sessionId, VolcengineSeedEditRequest request) {
        try {
            SimpleUserInfo user = UserContext.getUser();
            // 获取会话锁，防止并发提交同一会话的任务造成位置计算错误
            RLock sessionLock = redissonClient.getLock("session-lock:" + sessionId);
            if (!sessionLock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS)) {
                throw new BizException("会话锁定中，请稍后重试");
            }

            try {
                AiCreationSessionPo aiCreationSessionPo = aiCreationSessionMapper.selectOne(new LambdaQueryWrapper<AiCreationSessionPo>()
                        .eq(AiCreationSessionPo::getSessionId, sessionId)
                        .last("LIMIT 1"));
                if (aiCreationSessionPo == null) {
                    log.error("会话不存在");
                    throw new BizException("会话不存在");
                }
                // 使用Redisson原子计数器获取全局位置
                int globalPosition = (int) globalPositionCounter.incrementAndGet();

                // 使用Redisson获取会话位置计数器
                RAtomicLong sessionPositionCounter = redissonClient.getAtomicLong(SESSION_POSITION_PREFIX + sessionId);
                int sessionPosition = (int) sessionPositionCounter.incrementAndGet();


                AiImageTaskQueuePo taskQueuePo = imageTaskQueueMapper.selectOne(new LambdaQueryWrapper<AiImageTaskQueuePo>()
                        .eq(AiImageTaskQueuePo::getSessionId, request.getConversationId())
                        .eq(AiImageTaskQueuePo::getContentId, request.getContentId())
                        .eq(AiImageTaskQueuePo::getTaskType, TaskType.GENERATE.getValue())
                        .orderByAsc(AiImageTaskQueuePo::getCreateTime)
                        .last("LIMIT 1"));
                if (null == taskQueuePo) {
                    throw new BizException("未找到对应任务");
                }
                AiImageTaskQueuePo aiImageTaskQueuePo = BeanUtil.copyProperties(taskQueuePo, AiImageTaskQueuePo.class);
                aiImageTaskQueuePo.setTaskStatus(TaskStatus.PENDING.getValue());
                aiImageTaskQueuePo.setUpdateTime(new Date());
                aiImageTaskQueuePo.setId(null);
                aiImageTaskQueuePo.setTaskType(TaskType.EDIT.getValue());

                DoubaoImageGenerationRequest doubaoImageGenerationRequest = objectMapper.readValue(taskQueuePo.getRequestParams(), DoubaoImageGenerationRequest.class);
                DoubaoImageEditRequest editRequest = new DoubaoImageEditRequest();
                editRequest.setModel("doubao-seededit-3-0-i2i-250628");
                editRequest.setPrompt(request.getPrompt());
                editRequest.setImage(request.getImageUrls().get(0));
                editRequest.setResponseFormat("url");
                editRequest.setSize("adaptive");
                editRequest.setSeed(doubaoImageGenerationRequest.getSeed() != null ? doubaoImageGenerationRequest.getSeed() : -1);
                editRequest.setGuidanceScale(doubaoImageGenerationRequest.getGuidanceScale() != null ? doubaoImageGenerationRequest.getGuidanceScale() : 5.5);
                editRequest.setWatermark(false);
                String requestJson = JSON.toJSONString(editRequest);

                aiImageTaskQueuePo.setTaskInfo(null);
                aiImageTaskQueuePo.setRequestParams(requestJson);
                aiImageTaskQueuePo.setRetryCount(0);
                aiImageTaskQueuePo.setSessionQueuePosition(sessionPosition);
                aiImageTaskQueuePo.setGlobalQueuePosition(globalPosition);
                aiImageTaskQueuePo.setVendorAccountId(null);
                aiImageTaskQueuePo.setImageResult(null);
                aiImageTaskQueuePo.setImageModel("DOUBAO");
                imageTaskQueueMapper.insert(aiImageTaskQueuePo);
                return aiImageTaskQueuePo;
            } finally {
                // 确保会话锁被释放
                if (sessionLock.isHeldByCurrentThread()) {
                    sessionLock.unlock();
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while acquiring lock", e);
            throw new BizException("线程被中断，任务添加失败");
        } catch (Exception e) {
            log.error("Error queuing edit task", e);
            throw new BizException("Failed to queue edit task: " + e.getMessage());
        }
    }

    /**
     * 解析错误原因中的重试信息
     *
     * @param errorReason 错误原因字段
     * @return 重试信息对象，如果不是重试错误则返回null
     */
    private ErrorCodeRetryInfo parseErrorCodeRetryInfo(String errorReason) {
        if (StringUtils.isBlank(errorReason) || !errorReason.startsWith(ERROR_CODE_RETRY_FORMAT + ":")) {
            return null;
        }

        try {
            String jsonPart = errorReason.substring(ERROR_CODE_RETRY_FORMAT.length() + 1);
            return objectMapper.readValue(jsonPart, ErrorCodeRetryInfo.class);
        } catch (Exception e) {
            log.warn("Failed to parse error code retry info: {}", errorReason, e);
            return null;
        }
    }

    /**
     * 构建错误码重试信息字符串
     *
     * @param errorCode  错误码
     * @param retryCount 重试次数
     * @return 格式化的错误信息字符串
     */
    private String buildErrorCodeRetryInfo(String errorCode, int retryCount) {
        try {
            ErrorCodeRetryInfo retryInfo = new ErrorCodeRetryInfo();
            retryInfo.setErrorCode(errorCode);
            retryInfo.setRetryCount(retryCount);
            retryInfo.setLastRetryTime(new Date());

            String jsonInfo = objectMapper.writeValueAsString(retryInfo);
            return ERROR_CODE_RETRY_FORMAT + ":" + jsonInfo;
        } catch (Exception e) {
            log.error("Failed to build error code retry info", e);
            return ERROR_CODE_RETRY_FORMAT + ":重试次数" + retryCount + ",错误码" + errorCode;
        }
    }

    /**
     * 检查是否为需要重试的特定错误码
     *
     * @param errorCode 错误码
     * @return 是否为特定错误码
     */
    private boolean isSpecificErrorCode(String errorCode) {
        return SPECIFIC_ERROR_CODES.contains(errorCode);
    }

    /**
     * 带重试机制的任务处理方法
     *
     * @param task       要处理的任务
     * @param retryCount 当前重试次数
     * @return 处理结果
     */
    private ImageGenerateRes processTaskWithRetry(AiImageTaskQueuePo task, int retryCount) {
        try {
            // 更新任务状态为处理中
            if (retryCount == 0) {
                updateTaskStatus(task.getId(), TaskStatus.PROCESSING.getValue(), null, null, null);
            } else {
                log.info("Retrying task ID: {}, Type: {}, Retry count: {}",
                        task.getId(), task.getTaskType(), retryCount);
            }
            // 获取关联的供应商账号
            VendorAccountPo account = null;
            if (task.getVendorAccountId() != null) {
                account = vendorAccountService.getAccountById(task.getVendorAccountId());
                log.info("Using vendor account ID: {} for task ID: {}", task.getVendorAccountId(), task.getId());
            }
            // 执行实际的处理逻辑
            ImageGenerateRes result;
            String prompt = "";
            String userId = task.getUserId();
            if (TaskType.GENERATE.getValue().equals(task.getTaskType())) {
                VolcengineImageRequest request = objectMapper.readValue(task.getRequestParams(), VolcengineImageRequest.class);
                // 使用指定的账号凭证调用API
                prompt = request.getPrompt();
                result = volcengineImageApiClient.generateImageWithCredentials(request, account);

            } else if (TaskType.RETAIN.getValue().equals(task.getTaskType())) {
                VolcengineCharacterRetentionRequest request = objectMapper.readValue(task.getRequestParams(), VolcengineCharacterRetentionRequest.class);
                // 使用指定的账号凭证调用API
                prompt = request.getPrompt();
                result = volcengineImageApiClient.retainCharacterImageWithCredentials(request, account);
            } else if (TaskType.EDIT.getValue().equals(task.getTaskType())) {
                VolcengineSeedEditRequest request = objectMapper.readValue(task.getRequestParams(), VolcengineSeedEditRequest.class);
                result = volcengineImageApiClient.seedEditImageWithCredentials(request, account);
            } else {
                throw new IllegalArgumentException("Unknown task type: " + task.getTaskType());
            }

            // 检查结果，对特定错误码不进行重试
            if (StringUtils.isNotBlank(result.getCode()) && StringUtils.isBlank(result.getImageUrl())) {
                // 特定错误码：50411，50511，50412，50512，50413，这些情况尝试调用AI完成
                String code = result.getCode();
                if (SPECIFIC_ERROR_CODES.contains(code)) {
                    log.info("Task ID: {} returned error code {}, trying CompletionAPI instead",
                            task.getId(), code);

                    // 检查当前任务的重试次数
                    ErrorCodeRetryInfo retryInfo = parseErrorCodeRetryInfo(task.getErrorReason());
                    int currentRetryCount = 0;

                    if (retryInfo != null && code.equals(retryInfo.getErrorCode())) {
                        currentRetryCount = retryInfo.getRetryCount();
                    }

                    // 检查是否超过重试限制
                    if (currentRetryCount >= MAX_ERROR_CODE_RETRY_COUNT) {
                        log.warn("Task ID: {} has exceeded max retry count {} for error code {}, marking as failed",
                                task.getId(), MAX_ERROR_CODE_RETRY_COUNT, code);

                        String finalErrorReason = String.format("错误码%s重试次数已达上限(%d次)，最终失败",
                                code, MAX_ERROR_CODE_RETRY_COUNT);
                        updateTaskStatus(task.getId(), TaskStatus.FAILED.getValue(), null, finalErrorReason, null);

                        return new ImageGenerateRes(null, code, finalErrorReason);
                    }

                    // 调用AI完成API
                    String aiResponse = completionApiClient.requestCompletion(prompt, userId, null);

                    // 如果第一次调用返回null，重试一次
                    if (aiResponse == null) {
                        log.info("First attempt to CompletionAPI returned null, retrying once");
                        aiResponse = completionApiClient.requestCompletion(prompt, userId, null);
                    }

                    if (aiResponse != null) {
                        log.info("CompletionAPI succeeded, updating task with new prompt. Retry count: {}",
                                currentRetryCount + 1);

                        // 获取任务对象用于更新
                        AiImageTaskQueuePo updateTask = new AiImageTaskQueuePo();
                        updateTask.setId(task.getId());

                        // 更新任务状态为待执行
                        updateTask.setTaskStatus(TaskStatus.PENDING.getValue());

                        // 更新重试信息
                        String newErrorReason = buildErrorCodeRetryInfo(code, currentRetryCount + 1);
                        updateTask.setErrorReason(newErrorReason);

                        // 更新请求参数中的prompt
                        try {
                            if (TaskType.GENERATE.getValue().equals(task.getTaskType())) {
                                VolcengineImageRequest request = objectMapper.readValue(task.getRequestParams(), VolcengineImageRequest.class);
                                request.setPrompt(aiResponse);
                                updateTask.setRequestParams(objectMapper.writeValueAsString(request));
                            } else if (TaskType.RETAIN.getValue().equals(task.getTaskType())) {
                                VolcengineCharacterRetentionRequest request = objectMapper.readValue(task.getRequestParams(), VolcengineCharacterRetentionRequest.class);
                                request.setPrompt(aiResponse);
                                updateTask.setRequestParams(objectMapper.writeValueAsString(request));
                            } else if (TaskType.EDIT.getValue().equals(task.getTaskType())) {
                                VolcengineSeedEditRequest request = objectMapper.readValue(task.getRequestParams(), VolcengineSeedEditRequest.class);
                                request.setPrompt(aiResponse);
                                updateTask.setRequestParams(objectMapper.writeValueAsString(request));
                            }
                            updateTask.setUpdateTime(new Date());
                            imageTaskQueueMapper.updateById(updateTask);

                            // 创建一个新的结果对象表示处理成功
                            result = new ImageGenerateRes();
                            result.setCode("SUCCESS");
                            result.setMessage("Prompt has been updated with AI completion, retry count: " + (currentRetryCount + 1));
                        } catch (Exception e) {
                            log.error("Failed to update prompt with AI completion", e);
                        }
                    } else {
                        // AI完成API失败，增加重试计数但不重新提交任务
                        log.warn("CompletionAPI failed for task ID: {}, retry count: {}",
                                task.getId(), currentRetryCount + 1);

                        String errorReason = buildErrorCodeRetryInfo(code, currentRetryCount + 1);
                        updateTaskStatus(task.getId(), TaskStatus.FAILED.getValue(), null,
                                "AI完成API调用失败，错误码：" + code + "，重试次数：" + (currentRetryCount + 1), null);

                        return new ImageGenerateRes(null, code, "AI完成API调用失败");
                    }
                    // 返回处理结果
                    return result;
                } else {
                    throw new BizException(result.getCode(), result.getMessage());
                }
            }
            return result;
        } catch (Exception e) {
            log.error("Error processing task: {}", e.getMessage(), e);
            if (retryCount < MAX_RETRY_COUNT) {
                // 重试前等待1秒
                try {
                    log.info("等待1秒后进行第{}次重试", retryCount + 1);
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.warn("重试等待被中断", ie);
                }
                // 重试
                return processTaskWithRetry(task, retryCount + 1);
            }
            // 达到最大重试次数，标记为失败
            try {
                log.info("任务重试失败 Marking task as failed after {} retries", MAX_RETRY_COUNT);
                if (e instanceof BizException) {
                    return new ImageGenerateRes(null, ((BizException) e).getErrCode(), e.getMessage());
                }
            } catch (Exception ex) {
                log.error("Failed to update task status", ex);
            }
            return new ImageGenerateRes(null, "50000", e.getMessage());
        }
    }

//    /**
//     * 实现TaskProcessor接口的processImageTask方法
//     */
//    @Override
//    public boolean processImageTask(AiImageTaskQueuePo task) {
//        try {
//            processTaskWithRetry(task, 0);
//            return true;
//        } catch (Exception e) {
//            log.error("Failed to process task", e);
//            return false;
//        }
//    }

    @Override
    public ImageGenerateRes processTask(AiImageTaskQueuePo task) {
        try {
            return processTaskWithRetry(task, 0);
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error processing task", e);
            throw new BizException("任务处理失败: " + e.getMessage());
        }
    }

    @Override
    public boolean canExecuteImmediately() {
        // 使用Redisson信号量检查是否有可用许可
        // return taskSemaphore.availablePermits() > 0;
        return false;
    }

    @Override
    public AiImageTaskQueuePo getNextPendingTask() {
        // 查询下一个待处理任务
        LambdaQueryWrapper<AiImageTaskQueuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PENDING.getValue())
                .orderByAsc(AiImageTaskQueuePo::getGlobalQueuePosition)
                .last("LIMIT 1");
        return imageTaskQueueMapper.selectOne(queryWrapper);
    }

    @Override
    public List<AiImageTaskQueuePo> getTasksBySessionAndStatus(String sessionId, String status) {
        List<AiImageTaskQueuePo> poList = imageTaskQueueMapper.findBySessionIdAndTaskStatus(sessionId, status);
        return poList;
    }

    @Override
    public AiImageTaskQueuePo updateTaskStatus(Long taskId, String status, ImageGenerateRes result, String errorReason, Integer retryCount) {
        // 使用分布式锁确保状态更新的原子性
        AiImageTaskQueuePo task = getTaskById(taskId);
        if (task != null) {
            AiImageTaskQueuePo aiImageTaskQueuePo = new AiImageTaskQueuePo();
            aiImageTaskQueuePo.setId(taskId);
            aiImageTaskQueuePo.setTaskStatus(status);
            aiImageTaskQueuePo.setUserId(task.getUserId());

            // 设置重试次数（如果提供）
            if (retryCount != null) {
                aiImageTaskQueuePo.setRetryCount(retryCount);
            }

            if (result != null && StringUtils.isNotBlank(result.getImageUrl()) && Objects.equals(status, TaskStatus.COMPLETED.getValue())) {
                String imageUrl = ossUtil.uploadFile(result.getImageUrl(), OSS_PATH
                        .replace("{env}", env)
                        .replace("{sessionId}", task.getUserId())
                        .replace("{type}", "image") + IdUtil.fastSimpleUUID() + ".png");
                if (StringUtils.isBlank(imageUrl)) {
                    throw new BizException("上传图片失败");
                }
                aiImageTaskQueuePo.setImageResult(imageUrl);
                AiImageModifyRecordPo aiImageModifyRecordPo = aiImageModifyRecordMapper.selectOne(new LambdaQueryWrapper<AiImageModifyRecordPo>()
                        .eq(AiImageModifyRecordPo::getSessionId, task.getSessionId())
                        .eq(AiImageModifyRecordPo::getPrimaryId, task.getContentId())
                        .eq(AiImageModifyRecordPo::getContentType, task.getContentType())
                        .eq(AiImageModifyRecordPo::getStatus, 0)
                        .orderByDesc(AiImageModifyRecordPo::getCreateTime)
                        .last("Limit 1"));
                try {
                    if (Objects.equals(task.getContentType(), 3) && Objects.equals(task.getTaskType(), TaskType.GENERATE.getValue())) {
                        AiCreationContentPo aiCreationContentPo = aiCreationContentMapper.selectOne(new LambdaQueryWrapper<AiCreationContentPo>()
                                .eq(AiCreationContentPo::getSessionId, task.getSessionId())
                                .eq(AiCreationContentPo::getContentType, 3)
                                .last("Limit 1"));
                        if (Objects.nonNull(aiCreationContentPo)) {
                            String contentData = aiCreationContentPo.getContentData();
                            if (StringUtils.isNotBlank(contentData)) {
                                RoleSaveReq roleSaveReq = JSON.parseObject(contentData, RoleSaveReq.class);
                                roleSaveReq.getCharacters().forEach(character -> {
                                    if (Objects.equals(character.getCharID(), task.getContentId())) {
                                        character.setImage(aiImageTaskQueuePo.getImageResult());
                                        character.setImageStatus(TaskStatus.COMPLETED.getValue());
                                    }
                                });
                                String value = objectMapper.writeValueAsString(roleSaveReq);
                                aiCreationContentPo.setContentData(value);
                                aiCreationContentPo.setUpdateTime(new Date());
                                aiCreationContentMapper.updateById(aiCreationContentPo);
                            }
                        }
                    }
                    if (Objects.equals(task.getContentType(), 4) && Objects.equals(task.getTaskType(), TaskType.GENERATE.getValue())) {
                        AiShotPo aiShotPo = aiShotMapper.selectOne(new LambdaQueryWrapper<AiShotPo>()
                                .eq(AiShotPo::getSessionId, task.getSessionId())
                                .eq(AiShotPo::getShotId, task.getContentId())
                                .last("Limit 1"));
                        if (Objects.nonNull(aiShotPo)) {
                            String contentData = aiShotPo.getShotData();
                            if (StringUtils.isNotBlank(contentData)) {
                                ShotSaveReq.ShotGroupsDTO.ShotsDTO shotsDTO = JSON.parseObject(contentData, ShotSaveReq.ShotGroupsDTO.ShotsDTO.class);
                                shotsDTO.setImage(aiImageTaskQueuePo.getImageResult());
                                shotsDTO.setImageStatus(TaskStatus.COMPLETED.getValue());
                                String value = objectMapper.writeValueAsString(shotsDTO);
                                aiShotPo.setShotData(value);
                                aiShotPo.setUpdateTime(new Date());
                                aiShotMapper.updateById(aiShotPo);
                            }
                        }
                    }
                    if (Objects.equals(task.getContentType(), 0)) {
                        AiCreationContentPo aiCreationContentPo = aiCreationContentMapper.selectOne(new LambdaQueryWrapper<AiCreationContentPo>()
                                .eq(AiCreationContentPo::getSessionId, task.getSessionId())
                                .eq(AiCreationContentPo::getContentType, 5)
                                .last("Limit 1"));
                        if (Objects.nonNull(aiCreationContentPo)) {
                            String contentData = aiCreationContentPo.getContentData();
                            if (StringUtils.isNotBlank(contentData)) {
                                VisualSaveReq visualSaveReq = JSON.parseObject(contentData, VisualSaveReq.class);
                                VisualSaveReq.CoverDTO cover = visualSaveReq.getCover();
                                if (cover != null) {
                                    cover.setImage(aiImageTaskQueuePo.getImageResult());
                                }
                                String valueAsString = objectMapper.writeValueAsString(visualSaveReq);
                                aiCreationContentPo.setContentData(valueAsString);
                                aiCreationContentPo.setUpdateTime(new Date());
                                aiCreationContentMapper.updateById(aiCreationContentPo);

                                AiCreationVisualRecordPo one = visualRecordService.getOne(new LambdaQueryWrapper<AiCreationVisualRecordPo>()
                                        .eq(AiCreationVisualRecordPo::getSessionId, task.getSessionId())
                                        .eq(AiCreationVisualRecordPo::getCoverId, task.getContentId())
                                        .orderByDesc(AiCreationVisualRecordPo::getCreateTime)
                                        .last("Limit 1"));
                                if (one != null) {
                                    one.setCoverImage(aiImageTaskQueuePo.getImageResult());
                                    one.setContentData(valueAsString);
                                    one.setUpdateTime(new Date());
                                    visualRecordService.updateById(one);
                                }
                            }
                        }
                    }
                    if (Objects.equals(task.getTaskType(), TaskType.GENERATE_CANVAS.getValue()) || Objects.equals(task.getTaskType(), TaskType.EDIT_CANVAS.getValue())) {
                        // 使用Java 17的新特性发送异步事件通知
                        try {
                            // 使用switch表达式判断任务状态并发送相应事件
                            var eventAction = switch (aiImageTaskQueuePo.getTaskStatus()) {
                                case "COMPLETED" -> {
                                    log.info("画布生成任务完成，发送异步完成事件 - 任务ID: {}", aiImageTaskQueuePo.getId());
                                    eventPublishService.publishCanvasGenerateEvent(aiImageTaskQueuePo);
                                    yield "COMPLETION_EVENT_SENT";
                                }
                                case "FAILED" -> {
                                    log.warn("画布生成任务失败，发送异步失败事件 - 任务ID: {}", aiImageTaskQueuePo.getId());
                                    eventPublishService.publishCanvasGenerateEvent(aiImageTaskQueuePo);
                                    yield "FAILURE_EVENT_SENT";
                                }
                                case "PROCESSING" -> {
                                    log.debug("画布生成任务处理中，发送异步进度事件 - 任务ID: {}", aiImageTaskQueuePo.getId());
                                    eventPublishService.publishCanvasGenerateEvent(aiImageTaskQueuePo);
                                    yield "PROGRESS_EVENT_SENT";
                                }
                                default -> {
                                    log.debug("画布生成任务状态: {}, 发送通用事件 - 任务ID: {}",
                                            aiImageTaskQueuePo.getTaskStatus(), aiImageTaskQueuePo.getId());
                                    eventPublishService.publishCustomCanvasEvent(
                                            aiImageTaskQueuePo,
                                            "CANVAS_GENERATE_STATUS_CHANGE",
                                            "画布生成任务状态变更: " + aiImageTaskQueuePo.getTaskStatus()
                                    );
                                    yield "STATUS_CHANGE_EVENT_SENT";
                                }
                            };
                            // 使用Java 17的文本块记录详细日志
                            var logMessage = """
                                    画布生成异步事件处理完成:
                                    - 任务ID: %s
                                    - 任务状态: %s
                                    - 用户ID: %s
                                    - 事件动作: %s
                                    - 处理时间: %s
                                    """.formatted(
                                    aiImageTaskQueuePo.getId(),
                                    aiImageTaskQueuePo.getTaskStatus(),
                                    aiImageTaskQueuePo.getUserId(),
                                    eventAction,
                                    java.time.LocalDateTime.now()
                            );

                            log.info(logMessage);

                        } catch (Exception e) {
                            // 异步事件发送失败不应该影响主流程
                            log.error("发送画布生成异步事件失败 - 任务ID: {}, 错误: {}",
                                    task.getId(), e.getMessage(), e);
                        }
                    }

                } catch (JsonProcessingException e) {
                    log.error("Failed to parse contentData to RoleSaveReq", e);
                }

                if (aiImageModifyRecordPo != null) {
                    aiImageModifyRecordPo.setModifiedImageUrl(aiImageTaskQueuePo.getImageResult());
                    aiImageModifyRecordPo.setStatus(1);
                    aiImageModifyRecordPo.setUpdateTime(new Date());
                    aiImageModifyRecordMapper.updateById(aiImageModifyRecordPo);
                }
                //扣除用户积分，并且增加积分账单,编辑扣除10积分，其它扣除30积分
                if (Objects.equals(task.getTaskType(), TaskType.EDIT.getValue()) || Objects.equals(task.getTaskType(), TaskType.EDIT_CANVAS.getValue())) {
                    // 编辑任务扣除10积分
                    deductUserPoints(task.getUserId(), 30, task.getSessionId(), "图片编辑");
                } else if (Objects.equals(task.getTaskType(), TaskType.REDRAW.getValue())) {
                    // 其他任务扣除30积分
                    deductUserPoints(task.getUserId(), 30, task.getSessionId(), "图片重绘");
                } else {
                    // 扣除用户积分，并且增加积分账单
                    deductUserPoints(task.getUserId(), 30, task.getSessionId(), "图片生成");
                }
            } else {
                if (Objects.equals(task.getTaskType(), TaskType.GENERATE_CANVAS.getValue()) || Objects.equals(task.getTaskType(), TaskType.EDIT_CANVAS.getValue())) {
                    // 使用Java 17的新特性发送异步事件通知
                    try {
                        // 使用switch表达式判断任务状态并发送相应事件
                        var eventAction = switch (aiImageTaskQueuePo.getTaskStatus()) {
                            case "COMPLETED" -> {
                                log.info("画布生成任务完成，发送异步完成事件 - 任务ID: {}", aiImageTaskQueuePo.getId());
                                eventPublishService.publishCanvasGenerateEvent(aiImageTaskQueuePo);
                                yield "COMPLETION_EVENT_SENT";
                            }
                            case "FAILED" -> {
                                log.warn("画布生成任务失败，发送异步失败事件 - 任务ID: {}", aiImageTaskQueuePo.getId());
                                eventPublishService.publishCanvasGenerateEvent(aiImageTaskQueuePo);
                                yield "FAILURE_EVENT_SENT";
                            }
                            case "PROCESSING" -> {
                                log.debug("画布生成任务处理中，发送异步进度事件 - 任务ID: {}", aiImageTaskQueuePo.getId());
                                eventPublishService.publishCanvasGenerateEvent(aiImageTaskQueuePo);
                                yield "PROGRESS_EVENT_SENT";
                            }
                            default -> {
                                log.debug("画布生成任务状态: {}, 发送通用事件 - 任务ID: {}",
                                        aiImageTaskQueuePo.getTaskStatus(), aiImageTaskQueuePo.getId());
                                eventPublishService.publishCustomCanvasEvent(
                                        aiImageTaskQueuePo,
                                        "CANVAS_GENERATE_STATUS_CHANGE",
                                        "画布生成任务状态变更: " + aiImageTaskQueuePo.getTaskStatus()
                                );
                                yield "STATUS_CHANGE_EVENT_SENT";
                            }
                        };
                        // 使用Java 17的文本块记录详细日志
                        var logMessage = """
                                画布生成异步事件处理完成:
                                - 任务ID: %s
                                - 任务状态: %s
                                - 用户ID: %s
                                - 事件动作: %s
                                - 处理时间: %s
                                """.formatted(
                                aiImageTaskQueuePo.getId(),
                                aiImageTaskQueuePo.getTaskStatus(),
                                aiImageTaskQueuePo.getUserId(),
                                eventAction,
                                java.time.LocalDateTime.now()
                        );

                        log.info(logMessage);

                    } catch (Exception e) {
                        // 异步事件发送失败不应该影响主流程
                        log.error("发送画布生成异步事件失败 - 任务ID: {}, 错误: {}",
                                task.getId(), e.getMessage(), e);
                    }
                }
            }
            if (errorReason != null) {
                aiImageTaskQueuePo.setErrorReason(errorReason);
            }
            if (Objects.equals(status, TaskStatus.FAILED.getValue())) {
                AiImageModifyRecordPo aiImageModifyRecordPo = aiImageModifyRecordMapper.selectOne(new LambdaQueryWrapper<AiImageModifyRecordPo>()
                        .eq(AiImageModifyRecordPo::getSessionId, task.getSessionId())
                        .eq(AiImageModifyRecordPo::getPrimaryId, task.getContentId())
                        .eq(AiImageModifyRecordPo::getContentType, task.getContentType())
                        .eq(AiImageModifyRecordPo::getStatus, 0)
                        .orderByDesc(AiImageModifyRecordPo::getCreateTime)
                        .last("Limit 1"));
                if (aiImageModifyRecordPo != null) {
                    aiImageModifyRecordPo.setStatus(2);
                    aiImageModifyRecordPo.setFailureReason(errorReason);
                    aiImageModifyRecordPo.setUpdateTime(new Date());
                    aiImageModifyRecordMapper.updateById(aiImageModifyRecordPo);
                }
            }
            aiImageTaskQueuePo.setResult(result != null ? JSON.toJSONString(result) : null);
            aiImageTaskQueuePo.setUpdateTime(new Date());
            imageTaskQueueMapper.updateById(aiImageTaskQueuePo);

            return task;
        }
        return null;
    }

    /**
     * 扣除用户积分并记录积分交易
     *
     * @param userId        用户ID
     * @param points        扣除的积分数量
     * @param sessionId     会话ID
     * @param operationType 操作类型描述
     */
    @Transactional(rollbackFor = Exception.class)
    public void deductUserPoints(String userId, int points, String sessionId, String operationType) {
        if (points <= 0 || StringUtils.isBlank(userId)) {
            log.warn("无效的积分扣除请求: userId={}, points={}", userId, points);
            return;
        }

        try {
            // 查询用户信息
            AiUsersPo userPo = aiUsersMapper.selectOne(new LambdaQueryWrapper<AiUsersPo>()
                    .eq(AiUsersPo::getUserId, userId));

            if (userPo == null || userPo.getPoints() == null) {
                log.warn("用户不存在或积分为空: userId={}", userId);
                return;
            }

            // 计算剩余积分
            int remainingPoints = Math.max(0, userPo.getPoints() - points);

            // 更新用户积分
            userPo.setPoints(remainingPoints);
            userPo.setUpdateTime(new Date());
            aiUsersMapper.updateById(userPo);

            // 记录积分交易
            AiPointTransactionsPo transaction = new AiPointTransactionsPo();
            transaction.setUserId(userId);
            transaction.setPoints(-points); // 负数表示减少
            transaction.setBalance(remainingPoints);
            transaction.setType(3); // 3-会话消耗
            transaction.setReferenceId(sessionId);
            transaction.setDescription(operationType + "消耗" + points + "积分");
            transaction.setCreateTime(new Date());
            transaction.setUpdateTime(new Date());
            transaction.setDelFlag(0);
            aiPointTransactionsMapper.insert(transaction);

            log.info("用户积分扣除成功: userId={}, points={}, remainingPoints={}, operationType={}",
                    userId, points, remainingPoints, operationType);
        } catch (Exception e) {
            log.error("扣除用户积分失败: userId={}, points={}, error={}", userId, points, e.getMessage(), e);
            throw new BizException("扣除用户积分失败: " + e.getMessage());
        }
    }

    @Override
    public AiImageTaskQueuePo getTaskById(Long taskId) {
        return imageTaskQueueMapper.selectById(taskId);
    }

    /**
     * 错误码重试信息内部类
     */
    public static class ErrorCodeRetryInfo {
        private String errorCode;
        private int retryCount;
        private Date lastRetryTime;

        public ErrorCodeRetryInfo() {
        }

        public String getErrorCode() {
            return errorCode;
        }

        public void setErrorCode(String errorCode) {
            this.errorCode = errorCode;
        }

        public int getRetryCount() {
            return retryCount;
        }

        public void setRetryCount(int retryCount) {
            this.retryCount = retryCount;
        }

        public Date getLastRetryTime() {
            return lastRetryTime;
        }

        public void setLastRetryTime(Date lastRetryTime) {
            this.lastRetryTime = lastRetryTime;
        }
    }
} 