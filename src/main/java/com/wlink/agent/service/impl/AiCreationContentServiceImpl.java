package com.wlink.agent.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.dao.mapper.AiChapterMapper;
import com.wlink.agent.dao.mapper.AiShotMapper;
import com.wlink.agent.dao.mapper.AgentSoundMapper;
import com.wlink.agent.dao.mapper.AiCreationContentMapper;
import com.wlink.agent.dao.mapper.AiCreationSessionMapper;
import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.mapper.AiTtsRecordMapper;
import com.wlink.agent.dao.po.AiChapterPo;
import com.wlink.agent.dao.po.AiShotPo;
import com.wlink.agent.dao.po.AgentSoundPo;
import com.wlink.agent.dao.po.AiCreationContentPo;
import com.wlink.agent.dao.po.AiCreationSessionPo;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.dao.po.AiTtsRecordPo;
import com.wlink.agent.enums.TaskStatus;
import com.wlink.agent.enums.TaskType;
import com.wlink.agent.exception.ErrorCodeEnum;
import com.wlink.agent.model.dto.NotificationMessage;
import com.wlink.agent.model.req.DesignSaveReq;
import com.wlink.agent.model.req.NarrationSaveReq;
import com.wlink.agent.model.req.RoleSaveReq;
import com.wlink.agent.model.req.SceneSaveReq;
import com.wlink.agent.model.req.ShotSaveReq;
import com.wlink.agent.model.req.StorySaveReq;
import com.wlink.agent.model.req.TtsGenerateReq;
import com.wlink.agent.model.req.VisualSaveReq;
import com.wlink.agent.model.res.TtsGenerateRes;
import com.wlink.agent.service.AiCreationContentService;
import com.wlink.agent.service.AiCreationVisualRecordService;
import com.wlink.agent.utils.I18nMessageUtils;
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import com.wlink.agent.utils.OssUtils;
import com.wlink.agent.utils.ShotComparisonUtil;
import com.wlink.agent.utils.UserContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import com.wlink.agent.dao.mapper.AiTaskProgressRecordMapper;
import com.wlink.agent.dao.po.AiTaskProgressRecordPo;
import com.wlink.agent.model.req.TaskProgressSaveReq;
import com.wlink.agent.model.res.ShotAudioUpdateRes;
import com.wlink.agent.model.res.ShotTaskStatusRes;
import com.wlink.agent.enums.ShotResourceStatus;

import java.util.HashMap;
import com.wlink.agent.model.res.ShotTaskStatusGroupsRes;

/**
 * AI创作内容服务实现类
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class AiCreationContentServiceImpl extends ServiceImpl<AiCreationContentMapper, AiCreationContentPo> implements AiCreationContentService {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final AiCreationSessionMapper aiCreationSessionMapper;
    @Lazy
    private final AiCreationVisualRecordService visualRecordService;
    private final OssUtils ossUtils;
    private final RedissonClient redissonClient;
    private final AgentSoundMapper agentSoundMapper;
    private final MinimaxTtsService minimaxTtsService;
    private final AiTtsRecordMapper aiTtsRecordMapper;
    private final AiTaskProgressRecordMapper aiTaskProgressRecordMapper;
    private final AiImageTaskQueueMapper aiImageTaskQueueMapper;
    private final AiChapterMapper aiChapterMapper;
    private final AiShotMapper aiShotMapper;

    private static final String SSE_NOTIFICATION_TOPIC = "sse_operation_notification_topic";

    @Value("${spring.profiles.active}")
    String env;

    /**
     * oss路径
     *
     * @param conversationId 会话ID
     * @param storyData      故事数据
     */
    private static final String OSS_PATH = "dify/{env}/{sessionId}/{type}/";

    @Override
    public void saveStory(String conversationId, StorySaveReq storyData) {
        log.info("保存故事数据：{}", JSON.toJSONString(storyData));
        AiCreationSessionPo aiCreationSessionPo = getAiCreationSessionPo(conversationId);

        AiCreationContentPo aiCreationContentPo = getOne(new LambdaQueryWrapper<AiCreationContentPo>()
                .eq(AiCreationContentPo::getSessionId, aiCreationSessionPo.getSessionId())
                .eq(AiCreationContentPo::getContentType, 1)
                .last("Limit 1"));
        if (Objects.isNull(aiCreationContentPo)) {
            aiCreationContentPo = new AiCreationContentPo();
            aiCreationContentPo.setSessionId(aiCreationSessionPo.getSessionId());
            aiCreationContentPo.setContentType(1);
            aiCreationContentPo.setContentData(JSON.toJSONString(storyData));
            save(aiCreationContentPo);
        } else {
            String contentData = aiCreationContentPo.getContentData();
            StorySaveReq storySaveReq = JSON.parseObject(contentData, StorySaveReq.class);
            List<StorySaveReq.ChaptersDTO> existingChapters = storySaveReq.getChapters();
            if (CollUtil.isEmpty(existingChapters)) {
                existingChapters = new ArrayList<>();
            }
            
            // 处理新的chapters
            for (StorySaveReq.ChaptersDTO newChapter : storyData.getChapters()) {
                // 检查是否存在相同chapterID的章节
                boolean chapterExists = false;
                for (int i = 0; i < existingChapters.size(); i++) {
                    StorySaveReq.ChaptersDTO existingChapter = existingChapters.get(i);
                    if (existingChapter.getChapterID().equals(newChapter.getChapterID())) {
                        chapterExists = true;
                        // 如果章节已存在，则更新章节信息，但保留其中不在新章节中的场景
                        if (CollUtil.isNotEmpty(existingChapter.getScenes()) && CollUtil.isNotEmpty(newChapter.getScenes())) {
                            // 创建场景ID到场景的映射
                            Map<String, StorySaveReq.ChaptersDTO.ScenesDTO> existingSceneMap = existingChapter.getScenes().stream()
                                    .filter(scene -> scene != null && StringUtils.isNotBlank(scene.getId()))
                                    .collect(Collectors.toMap(
                                            StorySaveReq.ChaptersDTO.ScenesDTO::getId,
                                            scene -> scene,
                                            (existing, replacement) -> replacement
                                    ));
                            
                            // 更新或添加新场景
                            for (StorySaveReq.ChaptersDTO.ScenesDTO newScene : newChapter.getScenes()) {
                                if (newScene != null && StringUtils.isNotBlank(newScene.getId())) {
                                    existingSceneMap.put(newScene.getId(), newScene);
                                }
                            }
                            
                            // 将更新后的场景映射转换回列表
                            List<StorySaveReq.ChaptersDTO.ScenesDTO> updatedScenes = new ArrayList<>(existingSceneMap.values());
                            newChapter.setScenes(updatedScenes);
                        }
                        
                        // 更新章节
                        existingChapters.set(i, newChapter);
                        break;
                    }
                }
                
                // 如果是新章节，直接添加
                if (!chapterExists) {
                    existingChapters.add(newChapter);
                }
            }
            
            // 更新章节列表
            storySaveReq.setChapters(existingChapters);
            aiCreationContentPo.setContentData(JSON.toJSONString(storySaveReq));
            aiCreationContentPo.setUpdateTime(null);
            updateById(aiCreationContentPo);
        }
        publishNotification(conversationId, "1");
    }

    @Override
    public void updateStory(String conversationId, StorySaveReq storyData) {
        log.info("更新故事数据：conversationId={}, data={}", conversationId, JSON.toJSONString(storyData));
        AiCreationSessionPo aiCreationSessionPo = getAiCreationSessionPo(conversationId);
        AiCreationContentPo aiCreationContentPo = getOne(new LambdaQueryWrapper<AiCreationContentPo>()
                .eq(AiCreationContentPo::getSessionId, aiCreationSessionPo.getSessionId())
                .eq(AiCreationContentPo::getContentType, 1)
                .last("Limit 1"));
        if (Objects.isNull(aiCreationContentPo)) {
            aiCreationContentPo = new AiCreationContentPo();
            aiCreationContentPo.setSessionId(aiCreationSessionPo.getSessionId());
            aiCreationContentPo.setContentType(1);
            aiCreationContentPo.setContentData(JSON.toJSONString(storyData));
            save(aiCreationContentPo);
        } else {
            String contentData = aiCreationContentPo.getContentData();
            StorySaveReq storySaveReq = JSON.parseObject(contentData, StorySaveReq.class);
            List<StorySaveReq.ChaptersDTO> chapters = storySaveReq.getChapters();
            
            // 处理章节级别的更新
            Map<String, StorySaveReq.ChaptersDTO> chapterMap = chapters.stream()
                    .filter(chapter -> chapter != null && StringUtils.isNotBlank(chapter.getChapterID()))
                    .collect(Collectors.toMap(
                            StorySaveReq.ChaptersDTO::getChapterID, 
                            chapter -> chapter, 
                            (existing, replacement) -> existing
                    ));
            
            // 遍历新的章节数据
            for (StorySaveReq.ChaptersDTO newChapter : storyData.getChapters()) {
                if (newChapter != null && StringUtils.isNotBlank(newChapter.getChapterID())) {
                    StorySaveReq.ChaptersDTO existingChapter = chapterMap.get(newChapter.getChapterID());
                    
                    if (existingChapter != null) {
                        // 章节存在，需要处理场景级别的更新
                        Map<String, StorySaveReq.ChaptersDTO.ScenesDTO> sceneMap = new HashMap<>();
                        
                        // 收集现有场景
                        if (CollUtil.isNotEmpty(existingChapter.getScenes())) {
                            sceneMap = existingChapter.getScenes().stream()
                                    .filter(scene -> scene != null && StringUtils.isNotBlank(scene.getId()))
                                    .collect(Collectors.toMap(
                                            StorySaveReq.ChaptersDTO.ScenesDTO::getId,
                                            scene -> scene,
                                            (existing, replacement) -> existing
                                    ));
                        }
                        
                        // 更新场景
                        if (CollUtil.isNotEmpty(newChapter.getScenes())) {
                            for (StorySaveReq.ChaptersDTO.ScenesDTO newScene : newChapter.getScenes()) {
                                if (newScene != null && StringUtils.isNotBlank(newScene.getId())) {
                                    sceneMap.put(newScene.getId(), newScene);
                                }
                            }
                        }
                        
                        // 更新章节的其他属性
                        existingChapter.setChapterTitle(newChapter.getChapterTitle());
                        existingChapter.setTotalShots(newChapter.getTotalShots());
                        
                        // 更新章节的场景列表
                        List<StorySaveReq.ChaptersDTO.ScenesDTO> updatedScenes = new ArrayList<>(sceneMap.values());
                        updatedScenes.sort(Comparator.comparing(StorySaveReq.ChaptersDTO.ScenesDTO::getId, Comparator.nullsLast(String::compareTo)));
                        existingChapter.setScenes(updatedScenes);
                    } else {
                        // 章节不存在，直接添加
                        chapterMap.put(newChapter.getChapterID(), newChapter);
                    }
                }
            }
            
            // 更新章节列表
            List<StorySaveReq.ChaptersDTO> updatedChapters = new ArrayList<>(chapterMap.values());
            updatedChapters.sort(Comparator.comparing(StorySaveReq.ChaptersDTO::getChapterID, Comparator.nullsLast(String::compareTo)));
            storySaveReq.setChapters(updatedChapters);
            
            aiCreationContentPo.setContentData(JSON.toJSONString(storySaveReq));
            aiCreationContentPo.setUpdateTime(null);
            updateById(aiCreationContentPo);
        }
        publishNotification(conversationId, "1");
    }

    @Override
    public void deleteStory(String conversationId, StorySaveReq storyData) {
        log.info("删除故事数据：conversationId={}, data={}", conversationId, JSON.toJSONString(storyData));
        AiCreationSessionPo aiCreationSessionPo = getAiCreationSessionPo(conversationId);
        AiCreationContentPo aiCreationContentPo = getOne(new LambdaQueryWrapper<AiCreationContentPo>()
                .eq(AiCreationContentPo::getSessionId, aiCreationSessionPo.getSessionId())
                .eq(AiCreationContentPo::getContentType, 1)
                .last("Limit 1"));
        if (Objects.isNull(aiCreationContentPo)) {
            return;
        }
        
        String contentData = aiCreationContentPo.getContentData();
        StorySaveReq storySaveReq = JSON.parseObject(contentData, StorySaveReq.class);
        List<StorySaveReq.ChaptersDTO> chapters = storySaveReq.getChapters();
        
        // 处理删除逻辑
        if (CollUtil.isNotEmpty(storyData.getChapters())) {
            // 创建一个标记映射，用于记录要删除的章节和场景
            Map<String, Boolean> chaptersToDelete = new HashMap<>();
            Map<String, Map<String, Boolean>> scenesToDelete = new HashMap<>();
            
            // 收集要删除的章节和场景信息
            for (StorySaveReq.ChaptersDTO chapterToDelete : storyData.getChapters()) {
                if (chapterToDelete != null && StringUtils.isNotBlank(chapterToDelete.getChapterID())) {
                    // 如果没有指定场景，则删除整个章节
                    if (CollUtil.isEmpty(chapterToDelete.getScenes())) {
                        chaptersToDelete.put(chapterToDelete.getChapterID(), true);
                    } else {
                        // 否则，记录要删除的场景
                        Map<String, Boolean> chapterScenesToDelete = new HashMap<>();
                        for (StorySaveReq.ChaptersDTO.ScenesDTO sceneToDelete : chapterToDelete.getScenes()) {
                            if (sceneToDelete != null && StringUtils.isNotBlank(sceneToDelete.getId())) {
                                chapterScenesToDelete.put(sceneToDelete.getId(), true);
                            }
                        }
                        if (!chapterScenesToDelete.isEmpty()) {
                            scenesToDelete.put(chapterToDelete.getChapterID(), chapterScenesToDelete);
                        }
                    }
                }
            }
            
            // 处理删除
            java.util.Iterator<StorySaveReq.ChaptersDTO> chapterIterator = chapters.iterator();
            while (chapterIterator.hasNext()) {
                StorySaveReq.ChaptersDTO chapter = chapterIterator.next();
                if (chapter != null && StringUtils.isNotBlank(chapter.getChapterID())) {
                    // 检查是否需要删除整个章节
                    if (chaptersToDelete.containsKey(chapter.getChapterID())) {
                        chapterIterator.remove();
                        continue;
                    }
                    
                    // 检查是否需要删除章节中的特定场景
                    Map<String, Boolean> chapterScenesToDelete = scenesToDelete.get(chapter.getChapterID());
                    if (chapterScenesToDelete != null && CollUtil.isNotEmpty(chapter.getScenes())) {
                        // 删除指定的场景
                        chapter.getScenes().removeIf(scene -> 
                            scene != null && 
                            StringUtils.isNotBlank(scene.getId()) && 
                            chapterScenesToDelete.containsKey(scene.getId())
                        );
                    }
                }
            }
        }
        
        // 更新数据
        storySaveReq.setChapters(chapters);
        aiCreationContentPo.setContentData(JSON.toJSONString(storySaveReq));
        aiCreationContentPo.setUpdateTime(null);
        updateById(aiCreationContentPo);

        publishNotification(conversationId, "1");
    }

    @Override
    public void saveScene(String conversationId, SceneSaveReq sceneData) {

        log.info("保存场景数据：{}", JSON.toJSONString(sceneData));
        AiCreationSessionPo aiCreationSessionPo = getAiCreationSessionPo(conversationId);
        final int contentType = 2;

        AiCreationContentPo aiCreationContentPo = getOne(new LambdaQueryWrapper<AiCreationContentPo>()
                .eq(AiCreationContentPo::getSessionId, aiCreationSessionPo.getSessionId())
                .eq(AiCreationContentPo::getContentType, contentType)
                .last("Limit 1"));

        List<SceneSaveReq.ScenesDTO> scenes1 = sceneData.getScenes();
        if (CollUtil.isEmpty(scenes1)) {
            return;
        }
//        String path = OSS_PATH
//                .replace("{env}", env)
//                .replace("{sessionId}", conversationId)
//                .replace("{type}", "image");
//        scenes1.forEach(scene -> scene.setImage(ossUtils.uploadFile(scene.getImage(), path + IdUtil.fastSimpleUUID() + ".png")));
//        sceneData.setScenes(scenes1);
        if (Objects.isNull(aiCreationContentPo)) {
            aiCreationContentPo = new AiCreationContentPo();
            aiCreationContentPo.setSessionId(aiCreationSessionPo.getSessionId());
            aiCreationContentPo.setContentType(contentType);
            aiCreationContentPo.setContentData(JSON.toJSONString(sceneData));
            save(aiCreationContentPo);
        } else {
            String contentData = aiCreationContentPo.getContentData();
            SceneSaveReq existingSceneData = JSON.parseObject(contentData, SceneSaveReq.class);
            List<SceneSaveReq.ScenesDTO> scenes = existingSceneData.getScenes();
            if (CollUtil.isNotEmpty(scenes)) {
                // 移除 scenes 中与新 chapters 冲突（chapterID 相同）的旧元素
                scenes.removeIf(scene ->
                        scenes1.stream()
                                .anyMatch(newScene -> newScene.getId().equals(scene.getId()))
                );
                scenes.addAll(scenes1);
                List<SceneSaveReq.ScenesDTO> collect = scenes.stream().sorted(Comparator.comparing(SceneSaveReq.ScenesDTO::getId)).collect(Collectors.toList());
                existingSceneData.setScenes(collect);
            } else {
                existingSceneData.setScenes(scenes1);
            }
            aiCreationContentPo.setContentData(JSON.toJSONString(existingSceneData));
            aiCreationContentPo.setUpdateTime(null); // Or new Date() ? Consistent with Story logic
            updateById(aiCreationContentPo);
        }
        publishNotification(conversationId, "2");
    }

    @Override
    public void saveRole(String conversationId, RoleSaveReq roleData) {
        log.info("保存角色数据：{}", JSON.toJSONString(roleData));
        AiCreationSessionPo aiCreationSessionPo = getAiCreationSessionPo(conversationId);
        final int contentType = 3;

        AiCreationContentPo aiCreationContentPo = getOne(new LambdaQueryWrapper<AiCreationContentPo>()
                .eq(AiCreationContentPo::getSessionId, aiCreationSessionPo.getSessionId())
                .eq(AiCreationContentPo::getContentType, contentType)
                .last("Limit 1"));
        List<RoleSaveReq.CharactersDTO> characters1 = roleData.getCharacters();
        if (CollUtil.isEmpty(characters1)) {
            return;
        }
        characters1.forEach(character -> {
            if (StringUtils.isNotBlank(character.getImage()) && character.getImage().startsWith(MediaUrlPrefixUtil.MEDIA_URL_PREFIX)) {
                character.setImage(character.getImage().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, ""));
            }
        });
        roleData.setCharacters(characters1);
        if (Objects.isNull(aiCreationContentPo)) {
            aiCreationContentPo = new AiCreationContentPo();
            aiCreationContentPo.setSessionId(aiCreationSessionPo.getSessionId());
            aiCreationContentPo.setContentType(contentType);
            aiCreationContentPo.setContentData(JSON.toJSONString(roleData));
            save(aiCreationContentPo);
        } else {
            String contentData = aiCreationContentPo.getContentData();
            RoleSaveReq existingRoleData = JSON.parseObject(contentData, RoleSaveReq.class);
            List<RoleSaveReq.CharactersDTO> characters = existingRoleData.getCharacters();
            if (CollUtil.isNotEmpty(characters)) {

                characters.removeIf(charactersDTO ->
                        characters1.stream()
                                .anyMatch(newScene -> newScene.getCharID().equals(charactersDTO.getCharID()))
                );
                characters.addAll(characters1);
                List<RoleSaveReq.CharactersDTO> collect = characters.stream().sorted(Comparator.comparing(RoleSaveReq.CharactersDTO::getCharID)).collect(Collectors.toList());
                existingRoleData.setCharacters(collect);
            } else {
                existingRoleData.setCharacters(characters1);
            }
            aiCreationContentPo.setContentData(JSON.toJSONString(existingRoleData));
            aiCreationContentPo.setUpdateTime(null);
            updateById(aiCreationContentPo);
        }
        publishNotification(conversationId, "3");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveShot(String conversationId, ShotSaveReq shotData) {
        log.info("保存镜头数据：{}", JSON.toJSONString(shotData));
         getAiCreationSessionPo(conversationId);

        // 处理媒体URL前缀
        List<ShotSaveReq.ShotGroupsDTO> shotGroups = shotData.getShotGroups();
        if (CollUtil.isEmpty(shotGroups)) {
            return;
        }
        shotGroups.forEach(shotGroup -> {
            shotGroup.getShots().forEach(shot -> {
                if (null != shot && StringUtils.isNotBlank(shot.getImage()) && shot.getImage().startsWith(MediaUrlPrefixUtil.MEDIA_URL_PREFIX)) {
                    shot.setImage(shot.getImage().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, ""));
                }
            });
        });
        shotData.setShotGroups(shotGroups);

        // 保存到新表结构
        for (ShotSaveReq.ShotGroupsDTO shotGroup : shotGroups) {
            String segmentId = shotGroup.getSegmentId();
            String segmentName = shotGroup.getSegmentName();
            String sceneId = shotGroup.getSceneId();
            String sceneName = shotGroup.getSceneName();
            
            // 检查章节是否存在
            AiChapterPo chapterPo = aiChapterMapper.selectOne(
                    new LambdaQueryWrapper<AiChapterPo>()
                            .eq(AiChapterPo::getSessionId, conversationId)
                            .eq(AiChapterPo::getSegmentId, segmentId)
            );
            
            // 如果章节不存在，创建新章节
            if (chapterPo == null) {
                chapterPo = new AiChapterPo();
                chapterPo.setSessionId(conversationId);
                chapterPo.setSegmentId(segmentId);
                chapterPo.setSegmentName(segmentName);
                chapterPo.setSceneCount(1); // 初始场景数为1
                chapterPo.setDelFlag(0);
                aiChapterMapper.insert(chapterPo);
            } else {
                // 更新章节信息
                chapterPo.setSegmentName(segmentName);
                chapterPo.setSceneCount(0);
                aiChapterMapper.updateById(chapterPo);
            }
            
            // 保存分镜数据
            List<ShotSaveReq.ShotGroupsDTO.ShotsDTO> shots = shotGroup.getShots();
            if (CollUtil.isNotEmpty(shots)) {
                for (ShotSaveReq.ShotGroupsDTO.ShotsDTO shot : shots) {
                    if (null == shot){
                        continue;
                    }
                    // 检查分镜是否已存在
                    AiShotPo shotPo = aiShotMapper.selectOne(
                            new LambdaQueryWrapper<AiShotPo>()
                                    .eq(AiShotPo::getSessionId, conversationId)
                                    .eq(AiShotPo::getSegmentId, segmentId)
                                    .eq(AiShotPo::getShotId, shot.getId())
                    );
                    
                    if (shotPo == null) {
                        // 创建新分镜
                        shotPo = new AiShotPo();
                        shotPo.setSessionId(conversationId);
                        shotPo.setSegmentId(segmentId);
                        shotPo.setSceneId(sceneId);
                        shotPo.setSceneName(sceneName);
                        shotPo.setShotId(shot.getId());
                        shotPo.setShotData(JSON.toJSONString(shot));
                        shotPo.setQueue(Integer.valueOf(shot.getQueue()));

                        shotPo.setDelFlag(0);
                        aiShotMapper.insert(shotPo);
                    } else {
                        // 更新分镜数据
                        shotPo.setSegmentId(segmentId);
                        shotPo.setSceneId(sceneId);
                        shotPo.setSceneName(sceneName);
                        shotPo.setShotData(JSON.toJSONString(shot));
                        shotPo.setQueue(Integer.valueOf(shot.getQueue()));
                        aiShotMapper.updateById(shotPo);
                    }
                }
            }
        }
        log.info("处理分镜数据成功------发送页面通知");
        publishNotification(conversationId, "4");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveVisual(String conversationId, VisualSaveReq visualData) {
        log.info("保存视觉数据：{}", JSON.toJSONString(visualData));
        VisualSaveReq.CoverDTO cover = visualData.getCover();
//        cover.setImage(ossUtils.uploadFile(cover.getImage(), OSS_PATH
//                .replace("{env}", env)
//                .replace("{sessionId}", conversationId)
//                .replace("{type}", "image") + IdUtil.fastSimpleUUID() + ".png"));

        List<VisualSaveReq.ShotsDTO> shots = visualData.getShots();
        shots.forEach(shot -> {
            if (StringUtils.isNotBlank(shot.getSceneImage()) && shot.getSceneImage().startsWith(MediaUrlPrefixUtil.MEDIA_URL_PREFIX)) {
                shot.setSceneImage(shot.getSceneImage().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, ""));
            }
//
            if (StringUtils.isNotBlank(shot.getAudioClip()) && shot.getAudioClip().startsWith(MediaUrlPrefixUtil.MEDIA_URL_PREFIX)) {
                shot.setAudioClip(shot.getAudioClip().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, ""));
            }
        });
        VisualSaveReq.BackgroundMusicDTO backgroundMusic = visualData.getBackgroundMusic();
        if (Objects.nonNull(backgroundMusic)) {
            backgroundMusic.setAudioUrl(ossUtils.uploadFile(backgroundMusic.getAudioUrl(), OSS_PATH
                    .replace("{env}", env)
                    .replace("{sessionId}", conversationId)
                    .replace("{type}", "audio") + IdUtil.fastSimpleUUID() + ".wav"));
            //       backgroundMusic.setAudioUrl(StringUtils.isNotBlank(backgroundMusic.getAudioUrl()) ? MediaUrlPrefixUtil.MEDIA_URL_PREFIX + backgroundMusic.getAudioUrl() : "");
        }
        AiCreationSessionPo aiCreationSessionPo = getAiCreationSessionPo(conversationId);
        aiCreationSessionPo.setImageUrl(cover.getImage());
        aiCreationSessionPo.setTitle(cover.getTitle());
        aiCreationSessionPo.setStatus(1);
        aiCreationSessionPo.setDescription(cover.getSubtitle2());
        aiCreationSessionPo.setUpdateTime(new Date());
        aiCreationSessionMapper.updateById(aiCreationSessionPo);
        // 同时保存到内容表和视觉记录表
        saveContent(aiCreationSessionPo.getSessionId(), visualData);
        // 保存到视觉记录表
        visualRecordService.saveVisualRecord(conversationId, visualData, aiCreationSessionPo.getUserId());
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            log.error("线程休眠失败", e);
        }
        publishNotification(conversationId, "5");

    }

    private @NotNull AiCreationSessionPo getAiCreationSessionPo(String conversationId) {
        AiCreationSessionPo aiCreationSessionPo = aiCreationSessionMapper.selectOne(new LambdaQueryWrapper<AiCreationSessionPo>().eq(AiCreationSessionPo::getSessionId, conversationId));
        if (aiCreationSessionPo == null) {
            log.error("会话ID不存在");
            throw new BizException(ErrorCodeEnum.CONVERSATION_NOT_FOUND.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.CONVERSATION_NOT_FOUND.getMsg()));
        }
        return aiCreationSessionPo;
    }

    /**
     * 保存内容的通用方法
     *
     * @param sessionId 会话ID
     * @param data      数据对象
     * @return 保存结果
     */
    private void saveContent(String sessionId, VisualSaveReq data) {
        try {
            // 将数据对象转换为JSON字符串
            String contentData = objectMapper.writeValueAsString(data);

            AiCreationContentPo aiCreationContentPo = getOne(new LambdaQueryWrapper<AiCreationContentPo>()
                    .eq(AiCreationContentPo::getSessionId, sessionId)
                    .eq(AiCreationContentPo::getContentType, 5)
                    .last("Limit 1"));
            if (aiCreationContentPo != null) {
                aiCreationContentPo.setContentData(contentData);
                aiCreationContentPo.setUpdateTime(new Date());
                updateById(aiCreationContentPo);
            } else {
                AiCreationContentPo content = new AiCreationContentPo();
                content.setSessionId(sessionId);
                content.setContentType(5);
                content.setContentData(contentData);
                content.setUpdateTime(new Date());
                save(content);
            }
        } catch (Exception e) {
            log.error("Failed to convert data to JSON", e);
            throw new BizException(ErrorCodeEnum.CONTENT_DATA_FORMAT_ERROR.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.CONTENT_DATA_FORMAT_ERROR.getMsg()));
        }
    }


    @Override
    public Object getContentData(String conversationId, Integer contentType) {
        // 1. 验证会话是否存在
        String userId = UserContext.getUser().getUserId();

        AiCreationSessionPo aiCreationSessionPo = aiCreationSessionMapper.selectOne(new LambdaQueryWrapper<AiCreationSessionPo>()
                .eq(AiCreationSessionPo::getSessionId, conversationId)
                .eq(AiCreationSessionPo::getUserId, userId));

        if (aiCreationSessionPo == null) {
            log.error("会话不存在");
            throw new BizException(ErrorCodeEnum.CONVERSATION_NOT_FOUND.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.CONVERSATION_NOT_FOUND.getMsg()));
        }
        // 3. 查询内容数据
        LambdaQueryWrapper<AiCreationContentPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiCreationContentPo::getSessionId, aiCreationSessionPo.getSessionId())
                .eq(AiCreationContentPo::getContentType, contentType)
                .eq(AiCreationContentPo::getDelFlag, 0);
        AiCreationContentPo contentPo = getOne(wrapper);
        if (contentPo == null) {
            log.warn("未找到指定类型的内容数据, sessionId: {}, contentType: {}", aiCreationSessionPo.getSessionId(), contentType);
            return null;
        }
        String contentData = contentPo.getContentData();
        Object data = extracted(contentType, contentData, conversationId);
        // 4. 将JSON内容转换为对象并返回
        try {
            return data;
        } catch (Exception e) {
            log.error("解析内容数据失败", e);
            throw new BizException(ErrorCodeEnum.CONTENT_DATA_FORMAT_ERROR.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.CONTENT_DATA_FORMAT_ERROR.getMsg()));
        }
    }

    private Object extracted(Integer contentType, String contentData, String conversationId) {
        if (StringUtils.isBlank(contentData)) {
            return null;
        }
        switch (contentType) {
            case 1:
                StorySaveReq storySaveReq = JSON.parseObject(contentData, StorySaveReq.class);
                return storySaveReq;
            case 2:
                SceneSaveReq sceneSaveReq = JSON.parseObject(contentData, SceneSaveReq.class);
                sceneSaveReq.getScenes().forEach(scene -> {
                    scene.setImage(MediaUrlPrefixUtil.getMediaUrl(scene.getImage()));
                });
                return sceneSaveReq;
            case 3:
                RoleSaveReq roleSaveReq = JSON.parseObject(contentData, RoleSaveReq.class);
                roleSaveReq.getCharacters().forEach(character -> {
                    if (StringUtils.isNotBlank(character.getImage()) && !character.getImage().startsWith(MediaUrlPrefixUtil.MEDIA_URL_PREFIX)  && character.getImage().endsWith(".png")){
                        character.setImage(MediaUrlPrefixUtil.getMediaUrl(character.getImage()));
                    }
                    AiImageTaskQueuePo aiImageTaskQueuePo = aiImageTaskQueueMapper.selectOne(new LambdaQueryWrapper<AiImageTaskQueuePo>()
                            .eq(AiImageTaskQueuePo::getSessionId, conversationId)
                            .eq(AiImageTaskQueuePo::getContentId, character.getCharID())
                            .eq(AiImageTaskQueuePo::getContentType, contentType)
                            .eq(AiImageTaskQueuePo::getTaskType, TaskType.GENERATE.getValue())
                            .orderByDesc(AiImageTaskQueuePo::getCreateTime)
                            .last("limit 1"));
                    if (aiImageTaskQueuePo != null) {
                        character.setImageStatus(aiImageTaskQueuePo.getTaskStatus());
                        if (Objects.equals(aiImageTaskQueuePo.getTaskStatus(), TaskStatus.COMPLETED.getValue())) {
                            if(StringUtils.isBlank(character.getImage()) || !character.getImage().endsWith(".png")){
                                character.setImage(MediaUrlPrefixUtil.getMediaUrl(aiImageTaskQueuePo.getImageResult()));
                            }
                        }
                    } else {
                        if (StringUtils.isBlank(character.getImage())) {
                            character.setImageStatus(TaskStatus.PENDING_SUBMISSION.getValue());
                        } else {
                            character.setImageStatus(TaskStatus.COMPLETED.getValue());
                        }
                    }
                });
                return roleSaveReq;
            case 4:
                // 从新表结构中查询分镜数据
                ShotSaveReq shotSaveReq = new ShotSaveReq();
                List<ShotSaveReq.ShotGroupsDTO> shotGroups = new ArrayList<>();
                
                // 查询章节数据
                List<AiChapterPo> chapters = aiChapterMapper.selectList(
                        new LambdaQueryWrapper<AiChapterPo>()
                                .eq(AiChapterPo::getSessionId, conversationId)
                                .eq(AiChapterPo::getDelFlag, 0)
                );
                
                if (CollUtil.isNotEmpty(chapters)) {
                    // 查询分镜数据
                    for (AiChapterPo chapter : chapters) {
                        // 获取章节下的所有分镜
                        List<AiShotPo> shots = aiShotMapper.selectList(
                                new LambdaQueryWrapper<AiShotPo>()
                                        .eq(AiShotPo::getSessionId, conversationId)
                                        .eq(AiShotPo::getSegmentId, chapter.getSegmentId())
                                        .eq(AiShotPo::getDelFlag, 0)
                        );
                        
                        if (CollUtil.isEmpty(shots)) {
                            continue;
                        }
                        
                        // 按场景ID分组
                        Map<String, List<AiShotPo>> sceneMap = shots.stream()
                                .collect(Collectors.groupingBy(AiShotPo::getSceneId));
                        
                        // 构建分镜组数据
                        for (Map.Entry<String, List<AiShotPo>> entry : sceneMap.entrySet()) {
                            String sceneId = entry.getKey();
                            List<AiShotPo> sceneShots = entry.getValue();
                            
                            if (sceneShots.isEmpty()) {
                                continue;
                            }
                            
                            ShotSaveReq.ShotGroupsDTO shotGroup = new ShotSaveReq.ShotGroupsDTO();
                            shotGroup.setSceneId(sceneId);
                            shotGroup.setSceneName(sceneShots.get(0).getSceneName());
                            shotGroup.setSegmentId(chapter.getSegmentId());
                            shotGroup.setSegmentName(chapter.getSegmentName());
                            shotGroup.setTotalShots(sceneShots.size());
                            
                            List<ShotSaveReq.ShotGroupsDTO.ShotsDTO> shotList = new ArrayList<>();
                            
                            // 处理每个分镜
                            for (AiShotPo shot : sceneShots) {
                                ShotSaveReq.ShotGroupsDTO.ShotsDTO shotDTO = JSON.parseObject(
                                        shot.getShotData(), 
                                        ShotSaveReq.ShotGroupsDTO.ShotsDTO.class
                                );
                                
                                // 处理媒体URL前缀
                                if (StringUtils.isNotBlank(shotDTO.getImage()) && !shotDTO.getImage().startsWith(MediaUrlPrefixUtil.MEDIA_URL_PREFIX)
                                        && shotDTO.getImage().endsWith(".png")) {
                                    shotDTO.setImage(MediaUrlPrefixUtil.getMediaUrl(shotDTO.getImage()));
                                }
                                if (StringUtils.isNotBlank(shotDTO.getVoice()) && !shotDTO.getVoice().startsWith(MediaUrlPrefixUtil.MEDIA_URL_PREFIX)
                                        && shotDTO.getVoice().endsWith(".wav")) {
                                    shotDTO.setVoice(MediaUrlPrefixUtil.getMediaUrl(shotDTO.getVoice()));
                                }
                                
                                // 查询图片状态
                                AiImageTaskQueuePo aiImageTaskQueuePo = aiImageTaskQueueMapper.selectOne(
                                        new LambdaQueryWrapper<AiImageTaskQueuePo>()
                                                .eq(AiImageTaskQueuePo::getSessionId, conversationId)
                                                .eq(AiImageTaskQueuePo::getContentId, shotDTO.getId())
                                                .eq(AiImageTaskQueuePo::getContentType, contentType)
                                                .eq(AiImageTaskQueuePo::getTaskType, TaskType.GENERATE.getValue())
                                                .orderByDesc(AiImageTaskQueuePo::getCreateTime)
                                                .last("limit 1")
                                );
                                
                                if (aiImageTaskQueuePo != null) {
                                    shotDTO.setImageStatus(aiImageTaskQueuePo.getTaskStatus());
                                    if (Objects.equals(aiImageTaskQueuePo.getTaskStatus(), TaskStatus.COMPLETED.getValue())) {
                                        if (StringUtils.isBlank(shotDTO.getImage()) || !shotDTO.getImage().endsWith(".png")) {
                                            shotDTO.setImage(MediaUrlPrefixUtil.getMediaUrl(aiImageTaskQueuePo.getImageResult()));
                                        }
                                    }
                                } else {
                                    if (StringUtils.isBlank(shotDTO.getImage()) || !shotDTO.getImage().endsWith(".png")) {
                                        shotDTO.setImageStatus(TaskStatus.PENDING_SUBMISSION.getValue());
                                    } else {
                                        shotDTO.setImageStatus(TaskStatus.COMPLETED.getValue());
                                    }
                                }
                                
                                // 查询语音信息
                                AiTtsRecordPo narration = aiTtsRecordMapper.selectOne(
                                        new LambdaQueryWrapper<AiTtsRecordPo>()
                                                .eq(AiTtsRecordPo::getConversationId, conversationId)
                                                .eq(AiTtsRecordPo::getContentId, shotDTO.getId())
                                                .eq(AiTtsRecordPo::getType, "narration")
                                                .eq(AiTtsRecordPo::getStatus, 1)
                                                .orderByDesc(AiTtsRecordPo::getCreateTime)
                                                .last("limit 1")
                                );
                                
                                if (narration != null) {
                                    shotDTO.setDuration(narration.getAudioLength());
                                    shotDTO.setVoice(MediaUrlPrefixUtil.getMediaUrl(narration.getAudioUrl()));
                                }
                                
                                shotList.add(shotDTO);
                            }
                            
                            shotGroup.setShots(shotList);
                            shotGroups.add(shotGroup);
                        }
                    }
                }
                shotSaveReq.setShotGroups(shotGroups);
                return shotSaveReq;
            case 5:
                VisualSaveReq visualSaveReq = JSON.parseObject(contentData, VisualSaveReq.class);
                AiImageTaskQueuePo aiImageTaskQueuePo = aiImageTaskQueueMapper.selectOne(new LambdaQueryWrapper<AiImageTaskQueuePo>()
                        .eq(AiImageTaskQueuePo::getSessionId, conversationId)
                        .eq(AiImageTaskQueuePo::getContentType, 0)
                        .orderByDesc(AiImageTaskQueuePo::getCreateTime)
                        .last("limit 1"));
                if (aiImageTaskQueuePo != null) {
                    visualSaveReq.getCover().setImage(MediaUrlPrefixUtil.getMediaUrl(aiImageTaskQueuePo.getImageResult()));
                 }else {
                    visualSaveReq.getCover().setImage(MediaUrlPrefixUtil.getMediaUrl(visualSaveReq.getCover().getImage()));
                }
                visualSaveReq.getBackgroundMusic().setAudioUrl(MediaUrlPrefixUtil.getMediaUrl(visualSaveReq.getBackgroundMusic().getAudioUrl()));
                visualSaveReq.getShots().forEach(shot -> {
                    shot.setSceneImage(MediaUrlPrefixUtil.getMediaUrl(shot.getSceneImage()));
                    shot.setAudioClip(MediaUrlPrefixUtil.getMediaUrl(shot.getAudioClip()));
                });
                return visualSaveReq;
            case 10:
                JSONArray objects = JSON.parseArray(contentData);
                return objects;
            case 11:
                JSONArray jsonArray = JSON.parseArray(contentData);
                return jsonArray;
            default:
                throw new BizException(ErrorCodeEnum.UNSUPPORTED_CONTENT_TYPE.getCode(),
                        I18nMessageUtils.getMessage(ErrorCodeEnum.UNSUPPORTED_CONTENT_TYPE.getMsg()));
        }
    }

    @Override
    public Object getContentDataByDify(String conversationId, Integer contentType) {
        AiCreationSessionPo aiCreationSessionPo = aiCreationSessionMapper.selectOne(new LambdaQueryWrapper<AiCreationSessionPo>()
                .eq(AiCreationSessionPo::getSessionId, conversationId));

        if (aiCreationSessionPo == null) {
            log.error("会话不存在");
            throw new BizException(ErrorCodeEnum.CONVERSATION_NOT_FOUND.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.CONVERSATION_NOT_FOUND.getMsg()));
        }
        String contentData = null;
        // 3. 查询内容数据
        if (Objects.equals(contentType,4)){
            contentData = "分镜";
        }else {
            LambdaQueryWrapper<AiCreationContentPo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AiCreationContentPo::getSessionId, aiCreationSessionPo.getSessionId())
                    .eq(AiCreationContentPo::getContentType, contentType)
                    .eq(AiCreationContentPo::getDelFlag, 0);
            AiCreationContentPo contentPo = getOne(wrapper);
            if (contentPo == null) {
                log.warn("未找到指定类型的内容数据, sessionId: {}, contentType: {}", aiCreationSessionPo.getSessionId(), contentType);
                return null;
            }
            contentData = contentPo.getContentData();
        }
        Object extracted = extracted(contentType, contentData, conversationId);
        // 4. 将JSON内容转换为对象并返回
        try {
            return extracted;
        } catch (Exception e) {
            log.error("解析内容数据失败", e);
            throw new BizException(ErrorCodeEnum.CONTENT_DATA_FORMAT_ERROR.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.CONTENT_DATA_FORMAT_ERROR.getMsg()));
        }
    }

    @Override
    public void saveDesign(String conversationId, DesignSaveReq designData) {
        log.info("保存故事设计数据：conversationId={}, data={}", conversationId, JSON.toJSONString(designData));
        AiCreationSessionPo aiCreationSessionPo = getAiCreationSessionPo(conversationId);
        final int contentType = 10;
        AiCreationContentPo aiCreationContentPo = getOne(new LambdaQueryWrapper<AiCreationContentPo>()
                .eq(AiCreationContentPo::getSessionId, aiCreationSessionPo.getSessionId())
                .eq(AiCreationContentPo::getContentType, contentType)
                .last("Limit 1"));
        // 将数据对象转换为JSON字符串
        String contentData = null;
        if (Objects.isNull(aiCreationContentPo) && null != designData) {
            List<DesignSaveReq> designSaveReqs = new ArrayList<>();
            designSaveReqs.add(designData);
            AiCreationContentPo content = new AiCreationContentPo();
            content.setSessionId(conversationId);
            content.setContentType(contentType);
            content.setContentData(JSON.toJSONString(designSaveReqs));
            content.setUpdateTime(new Date());
            save(content);
        } else {
            contentData = aiCreationContentPo.getContentData();
            List<DesignSaveReq> designSaveReqs = JSON.parseArray(contentData, DesignSaveReq.class);
            AtomicReference<Boolean> isUpdate = new AtomicReference<>(false);
            if (CollUtil.isNotEmpty(designSaveReqs)){
                designSaveReqs.forEach(designSaveReq -> {
                    if (StringUtils.isNotBlank(designSaveReq.getKey()) && designSaveReq.getKey().equals(designData.getKey())) {
                        designSaveReq.setValue(designData.getValue());
                        isUpdate.set(true);
                    }
                });
            }
            if (Boolean.FALSE.equals(isUpdate.get())) {
                designSaveReqs.add(designData);
            }
            aiCreationContentPo.setContentData(JSON.toJSONString(designSaveReqs));
            aiCreationContentPo.setUpdateTime(new Date());
            updateById(aiCreationContentPo);
        }
        publishNotification(conversationId, "10");
        log.info("成功保存故事设计数据：conversationId={}", conversationId);
    }

    @Override
    public void saveNarration(String conversationId, List<NarrationSaveReq> narrationDataList) {
        log.info("保存旁白数据 (合并模式): conversationId={}, dataCount={}", conversationId, narrationDataList == null ? 0 : narrationDataList.size());
        if (CollUtil.isEmpty(narrationDataList)) {
            log.warn("输入旁白数据为空，不执行保存操作. conversationId: {}", conversationId);
            return;
        }
        AiCreationSessionPo aiCreationSessionPo = getAiCreationSessionPo(conversationId);
        final int contentType = 11;

        AiCreationContentPo aiCreationContentPo = getOne(new LambdaQueryWrapper<AiCreationContentPo>()
                .eq(AiCreationContentPo::getSessionId, aiCreationSessionPo.getSessionId())
                .eq(AiCreationContentPo::getContentType, contentType)
                .last("Limit 1"));

        if (Objects.isNull(aiCreationContentPo)) {
            // No existing data, save the new list directly
            aiCreationContentPo = new AiCreationContentPo();
            aiCreationContentPo.setSessionId(aiCreationSessionPo.getSessionId());
            aiCreationContentPo.setContentType(contentType);
            aiCreationContentPo.setContentData(JSON.toJSONString(narrationDataList));
            save(aiCreationContentPo);
            log.info("首次保存旁白数据成功. conversationId: {}", conversationId);
        } else {
            // Existing data found, merge the lists (add/replace based on ID)
            String existingContentData = aiCreationContentPo.getContentData();
            List<NarrationSaveReq> existingList = JSON.parseArray(existingContentData, NarrationSaveReq.class);
            if (existingList == null) {
                existingList = new ArrayList<>();
            }

            // Similar to saveStory: Remove existing items with IDs present in the new list, then add all new items.
            final java.util.Set<String> newIds = narrationDataList.stream()
                    .map(NarrationSaveReq::getSceneId) // Use getSceneId()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            existingList.removeIf(item -> item != null && newIds.contains(item.getSceneId())); // Use getSceneId()
            existingList.addAll(narrationDataList);

            // Optional: Sort the merged list by ID
            existingList = existingList.stream()
                    .filter(Objects::nonNull)
                    .sorted(Comparator.comparing(NarrationSaveReq::getSceneId)) // Use getSceneId()
                    .collect(Collectors.toList());

            aiCreationContentPo.setContentData(JSON.toJSONString(existingList));
            aiCreationContentPo.setUpdateTime(new Date());
            updateById(aiCreationContentPo);
            log.info("合并保存旁白数据成功. conversationId: {}", conversationId);
        }
        publishNotification(conversationId, String.valueOf(contentType));
    }

    @Override
    public void updateNarration(String conversationId, List<NarrationSaveReq> narrationDataList) {
        log.info("更新旁白数据 (覆盖/合并模式): conversationId={}, dataCount={}", conversationId, narrationDataList == null ? 0 : narrationDataList.size());
        if (CollUtil.isEmpty(narrationDataList)) {
            log.warn("输入旁白数据为空，不执行更新操作. conversationId: {}", conversationId);
            return;
        }
        AiCreationSessionPo aiCreationSessionPo = getAiCreationSessionPo(conversationId);
        final int contentType = 11;

        AiCreationContentPo aiCreationContentPo = getOne(new LambdaQueryWrapper<AiCreationContentPo>()
                .eq(AiCreationContentPo::getSessionId, aiCreationSessionPo.getSessionId())
                .eq(AiCreationContentPo::getContentType, contentType)
                .last("Limit 1"));

        if (Objects.isNull(aiCreationContentPo)) {
            // If content doesn't exist, create it (treat update as save)
            log.warn("更新旁白数据: 未找到现有数据，将创建新记录. conversationId: {}, contentType: {}", aiCreationSessionPo.getSessionId(), contentType);
            aiCreationContentPo = new AiCreationContentPo();
            aiCreationContentPo.setSessionId(aiCreationSessionPo.getSessionId());
            aiCreationContentPo.setContentType(contentType);
            aiCreationContentPo.setContentData(JSON.toJSONString(narrationDataList));
            save(aiCreationContentPo);
        } else {
            // Existing data found, merge using Map (similar to updateStory)
            String existingContentData = aiCreationContentPo.getContentData();
            List<NarrationSaveReq> existingList = JSON.parseArray(existingContentData, NarrationSaveReq.class);
            if (existingList == null) {
                existingList = new ArrayList<>();
            }

            // Convert existing list to map for efficient update/add
            Map<String, NarrationSaveReq> existingMap = existingList.stream()
                    .filter(item -> item != null && item.getSceneId() != null) // Use getSceneId()
                    .collect(Collectors.toMap(NarrationSaveReq::getSceneId, item -> item, (existing, replacement) -> replacement)); // Use getSceneId()

            // Add/update items from the input list into the map
            narrationDataList.forEach(newItem -> {
                if (newItem != null && newItem.getSceneId() != null) { // Use getSceneId()
                    existingMap.put(newItem.getSceneId(), newItem); // Use getSceneId()
                }
            });

            // Convert map back to sorted list
            List<NarrationSaveReq> updatedList = existingMap.values().stream()
                    .sorted(Comparator.comparing(NarrationSaveReq::getSceneId)) // Use getSceneId()
                    .collect(Collectors.toList());

            aiCreationContentPo.setContentData(JSON.toJSONString(updatedList));
            aiCreationContentPo.setUpdateTime(new Date());
            updateById(aiCreationContentPo);
        }
        log.info("成功更新旁白数据：conversationId={}", conversationId);

    }

    @Override
    public void deleteNarration(String conversationId, List<NarrationSaveReq> narrationDataListToDelete) {
        log.info("删除旁白数据: conversationId={}, itemsToDeleteCount={}", conversationId, narrationDataListToDelete == null ? 0 : narrationDataListToDelete.size());
        if (CollUtil.isEmpty(narrationDataListToDelete)) {
            log.warn("输入待删除旁白数据为空，不执行删除操作. conversationId: {}", conversationId);
            return;
        }
        AiCreationSessionPo aiCreationSessionPo = getAiCreationSessionPo(conversationId);
        final int contentType = 11;

        AiCreationContentPo aiCreationContentPo = getOne(new LambdaQueryWrapper<AiCreationContentPo>()
                .eq(AiCreationContentPo::getSessionId, aiCreationSessionPo.getSessionId())
                .eq(AiCreationContentPo::getContentType, contentType)
                .last("Limit 1"));

        if (Objects.isNull(aiCreationContentPo)) {
            log.warn("删除旁白数据: 未找到内容记录. SessionId: {}, ContentType: {}", aiCreationSessionPo.getSessionId(), contentType);
            return;
        }

        String existingContentData = aiCreationContentPo.getContentData();
        List<NarrationSaveReq> existingList = JSON.parseArray(existingContentData, NarrationSaveReq.class);

        if (CollUtil.isEmpty(existingList)) {
            log.warn("删除旁白数据: 现有内容列表为空，无需删除. SessionId: {}", aiCreationSessionPo.getSessionId());
            return; // Return void
        }

        // Similar to deleteStory: Use Map to efficiently remove items
        Map<String, NarrationSaveReq> existingMap = existingList.stream()
                .filter(item -> item != null && item.getSceneId() != null) // Use getSceneId()
                .collect(Collectors.toMap(NarrationSaveReq::getSceneId, item -> item)); // Use getSceneId()

        // Remove items specified in the input list from the map
        int itemsRemovedCount = 0;
        for (NarrationSaveReq itemToDelete : narrationDataListToDelete) {
            if (itemToDelete != null && itemToDelete.getSceneId() != null) { // Use getSceneId()
                if (existingMap.remove(itemToDelete.getSceneId()) != null) { // Use getSceneId()
                    itemsRemovedCount++;
                }
            }
        }

        if (itemsRemovedCount == 0) {
            log.info("删除旁白数据: 未找到与输入匹配的项，未执行删除. conversationId={}", conversationId);
            return; // Avoid unnecessary update if nothing changed
        }

        // Convert remaining map values back to sorted list
        List<NarrationSaveReq> updatedList = existingMap.values().stream()
                .sorted(Comparator.comparing(NarrationSaveReq::getSceneId)) // Use getSceneId()
                .collect(Collectors.toList());

        aiCreationContentPo.setContentData(JSON.toJSONString(updatedList));
        aiCreationContentPo.setUpdateTime(new Date());
        updateById(aiCreationContentPo);

        log.info("成功删除旁白数据项：conversationId={}, 删除了 {} 项", conversationId, itemsRemovedCount);

    }

    private void publishNotification(String conversationId, String operationType) {
        try {
            if (conversationId == null) return; // Cannot notify without target

            NotificationMessage notification = NotificationMessage.builder()
                    .conversationId(conversationId)
                    .eventPage(operationType)
                    .build();
            RTopic topic = redissonClient.getTopic(SSE_NOTIFICATION_TOPIC);
            topic.publish(notification);
            Thread.sleep(1000);
            log.info("Published notification to topic '{}' for conversationId: {}", SSE_NOTIFICATION_TOPIC, conversationId);
        } catch (Exception e) {
            log.error("Failed to publish SSE notification for conversationId: {}. Error: {}", conversationId, e.getMessage(), e);
        }
    }

    @Override
    public void updateScene(String conversationId, SceneSaveReq sceneData) {
        log.info("更新场景数据：conversationId={}, data={}", conversationId, JSON.toJSONString(sceneData));
        AiCreationSessionPo aiCreationSessionPo = getAiCreationSessionPo(conversationId);
        final int contentType = 2;

        AiCreationContentPo aiCreationContentPo = getOne(new LambdaQueryWrapper<AiCreationContentPo>()
                .eq(AiCreationContentPo::getSessionId, aiCreationSessionPo.getSessionId())
                .eq(AiCreationContentPo::getContentType, contentType)
                .last("Limit 1"));
        List<SceneSaveReq.ScenesDTO> scenes = sceneData.getScenes();
        if (Objects.isNull(scenes)) {
            log.warn("Cannot update scene items due to null scenes. Overwriting or skipping. SessionId: {}, ContentType: {}", aiCreationSessionPo.getSessionId(), contentType);
            return;
        }

//        scenes.forEach(scene -> {
//            scene.setImage(ossUtils.uploadFile(scene.getImage(), OSS_PATH
//                    .replace("{env}", env)
//                    .replace("{sessionId}", conversationId)
//                    .replace("{type}", "image") + IdUtil.fastSimpleUUID() + ".png"));
//        });
        if (Objects.isNull(aiCreationContentPo)) {
            aiCreationContentPo = new AiCreationContentPo();
            aiCreationContentPo.setSessionId(aiCreationSessionPo.getSessionId());
            aiCreationContentPo.setContentType(contentType);
            aiCreationContentPo.setContentData(JSON.toJSONString(sceneData));
            save(aiCreationContentPo);
        } else {
            String contentData = aiCreationContentPo.getContentData();
            SceneSaveReq existingSceneData = JSON.parseObject(contentData, SceneSaveReq.class);
            // Use getScenes() and ScenesDTO.getId()
            if (existingSceneData == null || existingSceneData.getScenes() == null) {
                log.warn("Cannot update scene items due to null data. Overwriting or skipping. SessionId: {}, ContentType: {}", aiCreationSessionPo.getSessionId(), contentType);
                aiCreationContentPo.setContentData(JSON.toJSONString(sceneData));
            } else {
                // Use ScenesDTO and getId()
                Map<String, SceneSaveReq.ScenesDTO> existingItemsMap = existingSceneData.getScenes().stream()
                        .filter(item -> item != null && item.getId() != null)
                        .collect(Collectors.toMap(SceneSaveReq.ScenesDTO::getId, item -> item, (existing, replacement) -> replacement));

                sceneData.getScenes().forEach(newItem -> {
                    if (newItem != null && newItem.getId() != null) {
                        existingItemsMap.put(newItem.getId(), newItem);
                    }
                });
                List<SceneSaveReq.ScenesDTO> collect = existingItemsMap.values().stream().sorted(Comparator.comparing(SceneSaveReq.ScenesDTO::getId)).collect(Collectors.toList());
                existingSceneData.setScenes(collect); // Use setScenes
                aiCreationContentPo.setContentData(JSON.toJSONString(existingSceneData));
            }
            aiCreationContentPo.setUpdateTime(null);
            updateById(aiCreationContentPo);
        }
        publishNotification(conversationId, "2");
    }

    @Override
    public void updateRole(String conversationId, RoleSaveReq roleData) {
        log.info("更新角色数据：conversationId={}, data={}", conversationId, JSON.toJSONString(roleData));
        AiCreationSessionPo aiCreationSessionPo = getAiCreationSessionPo(conversationId);
        final int contentType = 3;

        AiCreationContentPo aiCreationContentPo = getOne(new LambdaQueryWrapper<AiCreationContentPo>()
                .eq(AiCreationContentPo::getSessionId, aiCreationSessionPo.getSessionId())
                .eq(AiCreationContentPo::getContentType, contentType)
                .last("Limit 1"));
        List<RoleSaveReq.CharactersDTO> characters = roleData.getCharacters();
        if (Objects.isNull(characters)) {
            log.warn("Cannot update role items due to null characters. Overwriting or skipping. SessionId: {}, ContentType: {}", aiCreationSessionPo.getSessionId(), contentType);
            return;
        }
//        characters.forEach(character -> {
//            character.setImage(ossUtils.uploadFile(character.getImage(), OSS_PATH
//                    .replace("{env}", env)
//                    .replace("{sessionId}", conversationId)
//                    .replace("{type}", "image") + IdUtil.fastSimpleUUID() + ".png"));
//        });

        if (Objects.isNull(aiCreationContentPo)) {
            aiCreationContentPo = new AiCreationContentPo();
            aiCreationContentPo.setSessionId(aiCreationSessionPo.getSessionId());
            aiCreationContentPo.setContentType(contentType);
            aiCreationContentPo.setContentData(JSON.toJSONString(roleData));
            save(aiCreationContentPo);
        } else {
            String contentData = aiCreationContentPo.getContentData();
            RoleSaveReq existingRoleData = JSON.parseObject(contentData, RoleSaveReq.class);
            // Use getCharacters() and CharactersDTO.getCharID()
            if (existingRoleData == null || existingRoleData.getCharacters() == null) {
                log.warn("Cannot update role items due to null data. Overwriting or skipping. SessionId: {}, ContentType: {}", aiCreationSessionPo.getSessionId(), contentType);
                aiCreationContentPo.setContentData(JSON.toJSONString(roleData));
            } else {
                // Use CharactersDTO and getCharID()
                Map<String, RoleSaveReq.CharactersDTO> existingItemsMap = existingRoleData.getCharacters().stream()
                        .filter(item -> item != null && item.getCharID() != null)
                        .collect(Collectors.toMap(RoleSaveReq.CharactersDTO::getCharID, item -> item, (existing, replacement) -> replacement));

                roleData.getCharacters().forEach(newItem -> {
                    if (newItem != null && newItem.getCharID() != null) {
                        existingItemsMap.put(newItem.getCharID(), newItem);
                    }
                });
                List<RoleSaveReq.CharactersDTO> collect = existingItemsMap.values().stream().sorted(Comparator.comparing(RoleSaveReq.CharactersDTO::getCharID)).collect(Collectors.toList());
                existingRoleData.setCharacters(collect);
                aiCreationContentPo.setContentData(JSON.toJSONString(existingRoleData));
            }
            aiCreationContentPo.setUpdateTime(null);
            updateById(aiCreationContentPo);
        }
        publishNotification(conversationId, "3");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateShot(String conversationId, ShotSaveReq shotData) {
        log.info("更新分镜数据：conversationId={}, data={}", conversationId, JSON.toJSONString(shotData));
        AiCreationSessionPo aiCreationSessionPo = getAiCreationSessionPo(conversationId);
        final int contentType = 4;

        List<ShotSaveReq.ShotGroupsDTO> shotGroups = shotData.getShotGroups();
        if (Objects.isNull(shotGroups)) {
            log.warn("Cannot update shot groups due to null shotGroups. Overwriting or skipping. SessionId: {}, ContentType: {}", aiCreationSessionPo.getSessionId(), contentType);
            return;
        }

        // 更新新表结构中的数据
        for (ShotSaveReq.ShotGroupsDTO shotGroup : shotGroups) {
            String segmentId = shotGroup.getSegmentId();
            String segmentName = shotGroup.getSegmentName();
            String sceneId = shotGroup.getSceneId();
            String sceneName = shotGroup.getSceneName();
            
            // 更新章节信息
            AiChapterPo chapterPo = aiChapterMapper.selectOne(
                    new LambdaQueryWrapper<AiChapterPo>()
                            .eq(AiChapterPo::getSessionId, conversationId)
                            .eq(AiChapterPo::getSegmentId, segmentId)
            );
            
            if (chapterPo == null) {
                // 如果章节不存在，创建新章节
                chapterPo = new AiChapterPo();
                chapterPo.setSessionId(conversationId);
                chapterPo.setSegmentId(segmentId);
                chapterPo.setSegmentName(segmentName);
                chapterPo.setSceneCount(1); // 初始场景数为1
                chapterPo.setDelFlag(0);
                aiChapterMapper.insert(chapterPo);
            } else {
                // 更新章节信息
                chapterPo.setSegmentName(segmentName);
                // 重新计算场景数量
                Long count = aiShotMapper.selectCount(
                        new LambdaQueryWrapper<AiShotPo>()
                                .eq(AiShotPo::getSessionId, conversationId)
                                .eq(AiShotPo::getSegmentId, segmentId)
                );
                if (count == 0){
                    // 计算场景数量
                    int sceneCount = (int) shotGroups.stream()
                            .filter(group -> group.getSegmentId().equals(segmentId))
                            .map(ShotSaveReq.ShotGroupsDTO::getSceneId)
                            .distinct()
                            .count();
                    chapterPo.setSceneCount(sceneCount);
                }else{
                    chapterPo.setSceneCount(0);
                }
                aiChapterMapper.updateById(chapterPo);
            }
            // 更新分镜数据
            List<ShotSaveReq.ShotGroupsDTO.ShotsDTO> shots = shotGroup.getShots();
            if (CollUtil.isNotEmpty(shots)) {
                for (ShotSaveReq.ShotGroupsDTO.ShotsDTO shot : shots) {
                    if (null == shot){
                        continue;
                    }
                    // 处理图片URL
                    if (StringUtils.isNotBlank(shot.getImage()) && shot.getImage().startsWith(MediaUrlPrefixUtil.MEDIA_URL_PREFIX)) {
                        shot.setImage(shot.getImage().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, ""));
                    }
                    
                    // 检查分镜是否存在
                    AiShotPo shotPo = aiShotMapper.selectOne(
                            new LambdaQueryWrapper<AiShotPo>()
                                    .eq(AiShotPo::getSessionId, conversationId)
                                    .eq(AiShotPo::getSegmentId, segmentId)
                                    .eq(AiShotPo::getShotId, shot.getId())
                    );
                    
                    if (shotPo == null) {
                        // 创建新分镜
                        shotPo = new AiShotPo();
                        shotPo.setSessionId(conversationId);
                        shotPo.setSegmentId(segmentId);
                        shotPo.setSceneId(sceneId);
                        shotPo.setSceneName(sceneName);
                        shotPo.setShotId(shot.getId());
                        shotPo.setShotData(JSON.toJSONString(shot));
                        shotPo.setQueue(Integer.valueOf(shot.getQueue()));
                        shotPo.setDelFlag(0);
                        aiShotMapper.insert(shotPo);
                    } else {
                        // 更新分镜数据
                        shotPo.setSegmentId(segmentId);
                        shotPo.setSceneId(sceneId);
                        shotPo.setSceneName(sceneName);
                        shotPo.setShotData(JSON.toJSONString(shot));
                        shotPo.setQueue(Integer.valueOf(shot.getQueue()));
                        aiShotMapper.updateById(shotPo);
                    }
                }
            }
        }
        log.info("处理分镜数据成功------发送页面通知");
        publishNotification(conversationId, "4");
    }


    // --- Delete Methods ---


    @Override
    public void deleteScene(String conversationId, SceneSaveReq sceneData) {
        log.info("删除场景数据：conversationId={}, data={}", conversationId, JSON.toJSONString(sceneData));
        AiCreationSessionPo aiCreationSessionPo = getAiCreationSessionPo(conversationId);
        final int contentType = 2;

        AiCreationContentPo aiCreationContentPo = getOne(new LambdaQueryWrapper<AiCreationContentPo>()
                .eq(AiCreationContentPo::getSessionId, aiCreationSessionPo.getSessionId())
                .eq(AiCreationContentPo::getContentType, contentType)
                .last("Limit 1"));

        if (Objects.isNull(aiCreationContentPo) || sceneData == null || sceneData.getScenes() == null) {
            log.warn("Cannot delete scene items, content not found or input data invalid. SessionId: {}", aiCreationSessionPo.getSessionId());
            return; // Return void
        }

        String contentData = aiCreationContentPo.getContentData();
        SceneSaveReq existingSceneData = JSON.parseObject(contentData, SceneSaveReq.class);

        if (existingSceneData == null || existingSceneData.getScenes() == null) { // Use getScenes()
            log.warn("Cannot delete scene items, existing data invalid. SessionId: {}", aiCreationSessionPo.getSessionId());
            return; // Return void
        }

        // Use ScenesDTO and getId()
        Map<String, SceneSaveReq.ScenesDTO> existingItemsMap = existingSceneData.getScenes().stream()
                .filter(item -> item != null && item.getId() != null)
                .collect(Collectors.toMap(SceneSaveReq.ScenesDTO::getId, item -> item, (existing, replacement) -> replacement));

        sceneData.getScenes().forEach(itemToDelete -> { // Use getScenes()
            if (itemToDelete != null && itemToDelete.getId() != null) {
                existingItemsMap.remove(itemToDelete.getId());
            }
        });
        List<SceneSaveReq.ScenesDTO> collect = existingItemsMap.values().stream().sorted(Comparator.comparing(SceneSaveReq.ScenesDTO::getId)).collect(Collectors.toList());
        existingSceneData.setScenes(collect); // Use setScenes
        aiCreationContentPo.setContentData(JSON.toJSONString(existingSceneData));
        aiCreationContentPo.setUpdateTime(null);
        updateById(aiCreationContentPo);

        publishNotification(conversationId, "2");

    }

    @Override
    public void deleteRole(String conversationId, RoleSaveReq roleData) {
        log.info("删除角色数据：conversationId={}, data={}", conversationId, JSON.toJSONString(roleData));
        AiCreationSessionPo aiCreationSessionPo = getAiCreationSessionPo(conversationId);
        final int contentType = 3;

        AiCreationContentPo aiCreationContentPo = getOne(new LambdaQueryWrapper<AiCreationContentPo>()
                .eq(AiCreationContentPo::getSessionId, aiCreationSessionPo.getSessionId())
                .eq(AiCreationContentPo::getContentType, contentType)
                .last("Limit 1"));

        if (Objects.isNull(aiCreationContentPo) || roleData == null || roleData.getCharacters() == null) {
            log.warn("Cannot delete role items, content not found or input data invalid. SessionId: {}", aiCreationSessionPo.getSessionId());
            return; // Return void
        }

        String contentData = aiCreationContentPo.getContentData();
        RoleSaveReq existingRoleData = JSON.parseObject(contentData, RoleSaveReq.class);

        if (existingRoleData == null || existingRoleData.getCharacters() == null) { // Use getCharacters()
            log.warn("Cannot delete role items, existing data invalid. SessionId: {}", aiCreationSessionPo.getSessionId());
            return; // Return void
        }

        // Use CharactersDTO and getCharID()
        Map<String, RoleSaveReq.CharactersDTO> existingItemsMap = existingRoleData.getCharacters().stream()
                .filter(item -> item != null && item.getCharID() != null)
                .collect(Collectors.toMap(RoleSaveReq.CharactersDTO::getCharID, item -> item, (existing, replacement) -> replacement));

        roleData.getCharacters().forEach(itemToDelete -> { // Use getCharacters()
            if (itemToDelete != null && itemToDelete.getCharID() != null) {
                existingItemsMap.remove(itemToDelete.getCharID());
            }
        });
        List<RoleSaveReq.CharactersDTO> collect = existingItemsMap.values().stream().sorted(Comparator.comparing(RoleSaveReq.CharactersDTO::getCharID)).collect(Collectors.toList());
        existingRoleData.setCharacters(collect); // Use setCharacters
        aiCreationContentPo.setContentData(JSON.toJSONString(existingRoleData));
        aiCreationContentPo.setUpdateTime(null);
        updateById(aiCreationContentPo);
        publishNotification(conversationId, "3");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteShot(String conversationId, ShotSaveReq shotData) {
        log.info("删除分镜数据：conversationId={}, data={}", conversationId, JSON.toJSONString(shotData));
        AiCreationSessionPo aiCreationSessionPo = getAiCreationSessionPo(conversationId);
        final int contentType = 4;

        if (shotData == null || shotData.getShotGroups() == null) {
            log.warn("Cannot delete shot items, input data invalid. SessionId: {}", aiCreationSessionPo.getSessionId());
            return;
        }

        // 从新表结构中删除数据
        for (ShotSaveReq.ShotGroupsDTO shotGroup : shotData.getShotGroups()) {
            String segmentId = shotGroup.getSegmentId();
            String sceneId = shotGroup.getSceneId();
            
            // 删除分镜数据
            List<ShotSaveReq.ShotGroupsDTO.ShotsDTO> shots = shotGroup.getShots();
            if (CollUtil.isNotEmpty(shots)) {
                for (ShotSaveReq.ShotGroupsDTO.ShotsDTO shot : shots) {
                    if (null == shot){
                        continue;
                    }
                    // 删除分镜
                    aiShotMapper.delete(
                            new LambdaQueryWrapper<AiShotPo>()
                                    .eq(AiShotPo::getSessionId, conversationId)
                                    .eq(AiShotPo::getSegmentId, segmentId)
                                    .eq(AiShotPo::getShotId, shot.getId())
                    );
                }
            } else {
                // 如果未指定具体分镜，则删除整个场景的分镜
                aiShotMapper.delete(
                        new LambdaQueryWrapper<AiShotPo>()
                                .eq(AiShotPo::getSessionId, conversationId)
                                .eq(AiShotPo::getSegmentId, segmentId)
                                .eq(AiShotPo::getSceneId, sceneId)
                );
            }
            
            // 检查章节下是否还有分镜
            Long remainingShotCount = aiShotMapper.selectCount(
                    new LambdaQueryWrapper<AiShotPo>()
                            .eq(AiShotPo::getSessionId, conversationId)
                            .eq(AiShotPo::getSegmentId, segmentId)
            );
            
            if (remainingShotCount == 0) {
                // 如果章节下没有分镜，删除章节
                aiChapterMapper.delete(
                        new LambdaQueryWrapper<AiChapterPo>()
                                .eq(AiChapterPo::getSessionId, conversationId)
                                .eq(AiChapterPo::getSegmentId, segmentId)
                );
            } else {
                // 更新章节的场景数量
                AiChapterPo chapterPo = aiChapterMapper.selectOne(
                        new LambdaQueryWrapper<AiChapterPo>()
                                .eq(AiChapterPo::getSessionId, conversationId)
                                .eq(AiChapterPo::getSegmentId, segmentId)
                );
                
                if (chapterPo != null) {
                    // 重新计算场景数量
                    Long count = aiShotMapper.selectCount(
                            new LambdaQueryWrapper<AiShotPo>()
                                    .eq(AiShotPo::getSessionId, conversationId)
                                    .eq(AiShotPo::getSegmentId, segmentId)
                    );
                    if (count > 0){
                        // 重新计算场景数量
                        chapterPo.setSceneCount(0);
                    }else{
                        chapterPo.setSceneCount(0);
                    }
                    aiChapterMapper.updateById(chapterPo);
                }
            }
        }
        log.info("处理分镜数据成功------发送页面通知");
        publishNotification(conversationId, "4");
    }

    @Override
    public TtsGenerateRes generateTts(TtsGenerateReq req) {
        log.info("开始生成TTS音频，请求参数: {}", JSON.toJSONString(req));

        // 1. 查询声音配置
        AgentSoundPo agentSoundPo = agentSoundMapper.selectById(req.getVoiceId());
        if (agentSoundPo == null) {
            agentSoundPo = agentSoundMapper.selectOne(new LambdaQueryWrapper<AgentSoundPo>()
                    .eq(AgentSoundPo::getSound, req.getVoiceId())
                    .eq(AgentSoundPo::getDelFlag, 0)
                    .last("Limit 1"));
            if  (agentSoundPo == null) {
                log.error("未找到指定的 Voice ID: {}", req.getVoiceId());
                throw new BizException(ErrorCodeEnum.SOUND_NOT_EXISTS.getCode(),
                        I18nMessageUtils.getMessage(ErrorCodeEnum.SOUND_NOT_EXISTS.getMsg()));
            }
        }
        // 2. 创建并保存初始 TTS 记录
        AiTtsRecordPo ttsRecord = AiTtsRecordPo.builder()
                .conversationId(req.getConversationId())
                .voiceId(agentSoundPo.getId())
                .rate(req.getRate())
                .pitch(req.getPitch())
                .volume(req.getVolume())
                .contentId(req.getContentId())
                .emotion(req.getEmotion())
                .audioIndex(null == req.getIndex() ? 1 : req.getIndex())
                .type(req.getSoundType())
                .text(req.getText())
                .source(req.getSource())
                .tone(CollUtil.isNotEmpty(req.getTone()) ? StringUtils.join(req.getTone(), ",") : null)
                .status(0) // 0-待处理
                .createTime(new Date())
                .updateTime(new Date())
                .build();
        aiTtsRecordMapper.insert(ttsRecord);
        log.info("创建TTS记录成功, recordId: {}", ttsRecord.getId());

        String audioUrl = null;
        String errorMessage = null;
        int status = 1; // 默认成功
        TtsResult ttsResult = null;
        try {
            // 3. 调用 Minimax TTS 服务生成音频
            log.info("调用 Minimax TTS 服务生成音频...");
            ttsResult = minimaxTtsService.textToSpeechNonStream(req, agentSoundPo);
            if (ttsResult == null) {
                log.error("Minimax TTS 服务调用失败");
                throw new BizException("Minimax TTS 服务调用失败");
            }
            log.info("Minimax TTS 服务调用成功, 音频本地路径: {}", JSON.toJSONString(ttsResult));
            String ossPath = OSS_PATH.replace("{env}", env)
                    .replace("{sessionId}", req.getConversationId())
                    .replace("{type}", "audio") + IdUtil.fastSimpleUUID() + ".wav";
            // 注意：MinimaxTtsService 返回的是本地文件路径，如果需要OSS URL，需要在这里添加上传逻辑
            audioUrl = ossUtils.uploadFile(new File(ttsResult.getFilePath()), ossPath);
            try {
                // 3. 删除本地文件，并捕获删除结果
                Path path = Paths.get(ttsResult.getFilePath());
                Files.delete(path);
                log.debug("Local audio file deleted successfully: {}", ttsResult.getFilePath());
            } catch (Exception e) {
                log.error("Failed to delete local audio file: {}", ttsResult.getFilePath(), e);
            }
        } catch (BizException e) {
            log.error("TTS 服务调用失败: {}", e.getMessage(), e);
            status = 2; // 2-失败
            errorMessage = e.getMessage(); // 或者 e.getMessage()
        } catch (Exception e) {
            log.error("生成 TTS 音频时发生未知错误: {}", e.getMessage(), e);
            status = 2; // 2-失败
            errorMessage = "生成TTS时发生未知错误: " + e.getMessage();
        }

        // 4. 更新 TTS 记录状态和结果
        AiTtsRecordPo updateRecord = AiTtsRecordPo.builder()
                .id(ttsRecord.getId())
                .status(status)
                .audioLength(null != ttsResult ? ttsResult.getAudioLength() : 0L)
                .audioSize(null  != ttsResult ? ttsResult.getAudioSize() : 0L)
                .audioUrl(audioUrl) // 存储最终的URL（本地路径或OSS URL）
                .errorMessage(errorMessage)
                .updateTime(new Date())
                .build();
        aiTtsRecordMapper.updateById(updateRecord);
        log.info("更新TTS记录完成, recordId: {}, status: {}", ttsRecord.getId(), status);

        if (status == 2) {
            // 如果失败，可以抛出异常或在响应中指明
            throw new BizException(ErrorCodeEnum.TTS_GENERATION_FAILED.getCode(),
                    errorMessage != null ? errorMessage : I18nMessageUtils.getMessage(ErrorCodeEnum.TTS_GENERATION_FAILED.getMsg()));
        }
        // 5. 返回结果
        return new TtsGenerateRes(audioUrl, ttsRecord.getId(),updateRecord.getAudioLength());
    }

    @Override
    @Transactional // Add transaction management
    public void saveTaskProgress(String conversationId, TaskProgressSaveReq progressReq) {
        log.info("Saving task progress for conversationId: {},progressReq:{}", conversationId,JSON.toJSONString(progressReq));
        AiTaskProgressRecordPo existingRecord = aiTaskProgressRecordMapper.selectOne(new LambdaQueryWrapper<AiTaskProgressRecordPo>()
                .eq(AiTaskProgressRecordPo::getSessionId, conversationId)
                .last("LIMIT 1"));

        Date now = new Date();
        String progressJson;

        if (existingRecord == null) {
            // Insert new record
            log.info("No existing task progress found for conversationId: {}. Creating new record.", conversationId);
            AiTaskProgressRecordPo newRecord = new AiTaskProgressRecordPo();
            newRecord.setSessionId(conversationId);
            try {
                progressJson = objectMapper.writeValueAsString(progressReq);
                newRecord.setProgressData(progressJson);
            } catch (JsonProcessingException e) {
                log.error("Error serializing new task progress data to JSON for conversationId: {}", conversationId, e);
                throw new BizException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "无法序列化新的任务进度数据: " + e.getMessage());
            }
            newRecord.setCreateTime(now);
            newRecord.setUpdateTime(now);
            aiTaskProgressRecordMapper.insert(newRecord);
            log.info("Successfully inserted new task progress for conversationId: {}", conversationId);
        } else {
            // Update existing record
            log.info("Existing task progress found for conversationId: {}. Updating record.", conversationId);
            try {
                // Deserialize existing data
                TaskProgressSaveReq existingProgress = JSON.parseObject(existingRecord.getProgressData(), TaskProgressSaveReq.class);
                // Update fields if not null in the request
                if (progressReq.getProject() != null) {
                    existingProgress.setProject(progressReq.getProject());
                    log.debug("Updating project for conversationId: {}", conversationId);
                }
                if (CollUtil.isNotEmpty(progressReq.getTodo())) { // Check if the list is not empty
                    existingProgress.setTodo(progressReq.getTodo());
                    log.debug("Updating todo list for conversationId: {}", conversationId);
                }
                if (CollUtil.isNotEmpty(progressReq.getChapters())) { // Check if the list is not empty
                    existingProgress.setChapters(progressReq.getChapters());
                    log.debug("Updating chapters list for conversationId: {}", conversationId);
                }
                if (StringUtils.isNotBlank(progressReq.getNote())) {
                    existingProgress.setNote(progressReq.getNote());
                    log.debug("Updating note for conversationId: {}", conversationId);
                }
                // Serialize updated data
                progressJson = objectMapper.writeValueAsString(existingProgress);
                existingRecord.setProgressData(progressJson);
            } catch (JsonProcessingException e) {
                log.error("Error processing task progress JSON for update, conversationId: {}", conversationId, e);
                throw new BizException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "处理任务进度数据更新时出错: " + e.getMessage());
            }
            existingRecord.setUpdateTime(now);
            aiTaskProgressRecordMapper.updateById(existingRecord);
            log.info("Successfully updated task progress for conversationId: {}", conversationId);
        }
    }

    @Override
    public Object getLatestTaskProgress(String conversationId) {
        log.info("查询最新任务进度：conversationId={}", conversationId);
        
        // 验证会话是否存在
        AiCreationSessionPo aiCreationSessionPo = aiCreationSessionMapper.selectOne(
                new LambdaQueryWrapper<AiCreationSessionPo>()
                .eq(AiCreationSessionPo::getSessionId, conversationId)
        );

        if (aiCreationSessionPo == null) {
            log.error("会话不存在: {}", conversationId);
            throw new BizException(ErrorCodeEnum.CONVERSATION_NOT_FOUND.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.CONVERSATION_NOT_FOUND.getMsg()));
        }
        
        // 查询最新的任务进度记录
        AiTaskProgressRecordPo latestRecord = aiTaskProgressRecordMapper.selectOne(
                new LambdaQueryWrapper<AiTaskProgressRecordPo>()
                .eq(AiTaskProgressRecordPo::getSessionId, conversationId)
                .orderByDesc(AiTaskProgressRecordPo::getCreateTime)
                .last("LIMIT 1")
        );
        
        if (latestRecord == null) {
            log.warn("未找到任务进度记录: {}", conversationId);
            return null;
        }
        
        try {
            // 将进度数据解析为对象
            return JSON.parse(latestRecord.getProgressData());
        } catch (Exception e) {
            log.error("解析任务进度数据失败: {}", e.getMessage(), e);
            throw new BizException(ErrorCodeEnum.CONTENT_DATA_FORMAT_ERROR.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.CONTENT_DATA_FORMAT_ERROR.getMsg()));
        }
    }
    
    /**
     * 更新会话内容
     *
     * @param conversationId 会话ID
     * @param contentType 内容类型(1-故事,2-场景,3-角色,4-分镜,5-视觉,10-故事设计,11-故事旁白)
     * @param contentData 内容数据
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ShotAudioUpdateRes> updateConversationContent(String conversationId, Integer contentType, Object contentData) {
        log.info("更新会话内容：conversationId={}, contentType={}", conversationId, contentType);
        
        // 验证会话是否存在
        AiCreationSessionPo aiCreationSessionPo = aiCreationSessionMapper.selectOne(
                new LambdaQueryWrapper<AiCreationSessionPo>()
                .eq(AiCreationSessionPo::getSessionId, conversationId)
        );
        if (aiCreationSessionPo == null) {
            log.error("会话不存在: {}", conversationId);
            throw new BizException(ErrorCodeEnum.CONVERSATION_NOT_FOUND.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.CONVERSATION_NOT_FOUND.getMsg()));
        }
        // 查询内容是否存在
        AiCreationContentPo contentPo = getOne(
                new LambdaQueryWrapper<AiCreationContentPo>()
                .eq(AiCreationContentPo::getSessionId, conversationId)
                .eq(AiCreationContentPo::getContentType, contentType)
                .eq(AiCreationContentPo::getDelFlag, 0)
        );
        
        if (contentPo == null) {
            log.error("内容数据不存在: conversationId={}, contentType={}", conversationId, contentType);
            throw new BizException("CONTENT_NOT_FOUND", "内容数据不存在");
        }
        
        // 创建音频更新信息列表
        List<ShotAudioUpdateRes> audioUpdateList = new ArrayList<>();
        
        try {
            // 将内容数据转换为JSON字符串
            String jsonContent = objectMapper.writeValueAsString(contentData);
            if (Objects.equals(contentType, 4)){
                String contentPoContentData = contentPo.getContentData();
                ShotSaveReq shotSaveReq = JSON.parseObject(contentPoContentData, ShotSaveReq.class);
                ShotSaveReq shotSaveReq1 = JSON.parseObject(jsonContent, ShotSaveReq.class);
                List<String> ids = ShotComparisonUtil.compareNarrationStream(shotSaveReq, shotSaveReq1);
                shotSaveReq1.getShotGroups().forEach(shotGroups -> {
                    shotGroups.getShots().forEach(shot -> {
                        shot.setImage(StringUtils.isNotBlank(shot.getImage()) ? shot.getImage().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, "") : "");
                        shot.setVoice(StringUtils.isNotBlank(shot.getVoice()) ? shot.getVoice().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, "") : "");
                        if (ids.contains(shot.getId()) && StringUtils.isNotBlank(shot.getNarration())) {
                            AiCreationContentPo aiCreationContentPo = getOne(new LambdaQueryWrapper<AiCreationContentPo>()
                                    .eq(AiCreationContentPo::getSessionId, conversationId)
                                    .eq(AiCreationContentPo::getContentType, 10)
                                    .last("limit 1"));

                        }
                    });
                });
                jsonContent = objectMapper.writeValueAsString(shotSaveReq1);
            }
            if (Objects.equals(contentType, 3)){
                String contentPoContentData = jsonContent;
                RoleSaveReq roleSaveReq = JSON.parseObject(contentPoContentData, RoleSaveReq.class);
                roleSaveReq.getCharacters().forEach(character -> {
                    character.setImage(StringUtils.isNotBlank(character.getImage()) ? character.getImage().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, "") : "");
                });
                jsonContent = objectMapper.writeValueAsString(roleSaveReq);
            }
            // 更新内容数据
            contentPo.setContentData(jsonContent);
            contentPo.setUpdateTime(new Date());
            updateById(contentPo);
            
            // 如果有音频更新，通知客户端
            if (!audioUpdateList.isEmpty()) {
                log.info("分镜音频已更新: conversationId={}, updatedCount={}", conversationId, audioUpdateList.size());
            }
            
            return audioUpdateList;
        } catch (JsonProcessingException e) {
            log.error("更新会话内容失败：转换内容数据为JSON字符串时出错", e);
            throw new BizException(ErrorCodeEnum.CONTENT_DATA_FORMAT_ERROR.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.CONTENT_DATA_FORMAT_ERROR.getMsg()));
        }
    }

//    @Override
//    public List<ShotTaskStatusRes> getShotTaskStatus(String conversationId, String contentId, Integer contentIdType) {
//        log.info("查询分镜任务状态: conversationId={}, contentId={}, contentIdType={}", conversationId, contentId, contentIdType);
//
//        // 查询分镜内容
//        AiCreationSessionPo aiCreationSessionPo = getAiCreationSessionPo(conversationId);
//        final int contentType = 4; // 分镜内容类型固定为4
//
//        // 查询分镜内容数据
//        AiCreationContentPo aiCreationContentPo = getOne(new LambdaQueryWrapper<AiCreationContentPo>()
//                .eq(AiCreationContentPo::getSessionId, aiCreationSessionPo.getSessionId())
//                .eq(AiCreationContentPo::getContentType, contentType)
//                .eq(AiCreationContentPo::getDelFlag, 0)
//                .last("Limit 1"));
//
//        if (aiCreationContentPo == null || StringUtils.isBlank(aiCreationContentPo.getContentData())) {
//            log.warn("未找到分镜内容数据, sessionId: {}", aiCreationSessionPo.getSessionId());
//            return Collections.emptyList();
//        }
//
//        // 解析分镜数据
//        ShotSaveReq shotSaveReq = JSON.parseObject(aiCreationContentPo.getContentData(), ShotSaveReq.class);
//        List<ShotTaskStatusRes> resultList = new ArrayList<>();
//
//        // 根据contentIdType不同，进行不同的处理
//        if (contentIdType == 1) {
//            // 章节ID，解析所有分镜状态
//            shotSaveReq.getShotGroups().forEach(shotGroup ->
//                shotGroup.getShots().forEach(shot ->
//                    resultList.add(buildShotTaskStatus(shot, shotGroup.getSceneId(), conversationId))
//                )
//            );
//        } else if (contentIdType == 2) {
//            // 场景ID，解析该场景下所有分镜状态
//            shotSaveReq.getShotGroups().stream()
//                    .filter(shotGroup -> contentId.equals(shotGroup.getSceneId()))
//                    .forEach(shotGroup ->
//                        shotGroup.getShots().forEach(shot ->
//                            resultList.add(buildShotTaskStatus(shot, shotGroup.getSceneId(), conversationId))
//                        )
//                    );
//        } else if (contentIdType == 3) {
//            // 分镜ID，仅解析该分镜状态
//            shotSaveReq.getShotGroups().forEach(shotGroup ->
//                shotGroup.getShots().stream()
//                        .filter(shot -> contentId.equals(shot.getId()))
//                        .forEach(shot ->
//                            resultList.add(buildShotTaskStatus(shot, shotGroup.getSceneId(), conversationId))
//                        )
//            );
//        } else {
//            log.warn("不支持的内容ID类型: {}", contentIdType);
//        }
//
//        return resultList;
//    }

    /**
     * 构建分镜任务状态对象
     * 
     * @param shot 分镜对象
     * @param sceneId 场景ID
     * @param conversationId 会话ID
     * @return 分镜任务状态
     */
    private ShotTaskStatusRes buildShotTaskStatus(ShotSaveReq.ShotGroupsDTO.ShotsDTO shot, String sceneId, String conversationId) {
        // 检查图片状态
        String imageStatus = checkResourceStatus(shot.getImage(), shot.getImageStatus(), ".png", conversationId, shot.getId(), "image");
        
        // 检查声音状态
        String voiceStatus = checkResourceStatus(shot.getVoice(), null, ".wav", conversationId, shot.getId(), "voice");
        
        // 检查旁白状态
        String narrationStatus = StringUtils.isNotBlank(shot.getNarration()) ? 
                ShotResourceStatus.COMPLETED.getValue() : ShotResourceStatus.NOT_EXIST.getValue();
        
        return ShotTaskStatusRes.builder()
                .shotId(shot.getId())
                .sceneId(sceneId)
                .imageStatus(imageStatus)
                .voiceStatus(voiceStatus)
                .narrationStatus(narrationStatus)
                .build();
    }

    /**
     * 检查资源状态
     * 
     * @param resourceUrl 资源URL
     * @param resourceStatus 资源状态
     * @param fileExtension 文件扩展名
     * @param conversationId 会话ID
     * @param contentId 内容ID(shotId)
     * @param resourceType 资源类型(image/voice)
     * @return 资源状态
     */
    private String checkResourceStatus(String resourceUrl, String resourceStatus, String fileExtension, 
                                      String conversationId, String contentId, String resourceType) {
        // 对于图片资源，优先查询任务表获取状态
        if ("image".equals(resourceType)) {
            // 查询图片任务
            AiImageTaskQueuePo aiImageTaskQueuePo = aiImageTaskQueueMapper.selectOne(new LambdaQueryWrapper<AiImageTaskQueuePo>()
                    .eq(AiImageTaskQueuePo::getSessionId, conversationId)
                    .eq(AiImageTaskQueuePo::getContentId, contentId)
                    .eq(AiImageTaskQueuePo::getContentType, 4) // 分镜内容类型固定为4
                    .eq(AiImageTaskQueuePo::getTaskType, TaskType.GENERATE.getValue())
                    .orderByDesc(AiImageTaskQueuePo::getCreateTime)
                    .last("limit 1"));
            
            if (aiImageTaskQueuePo != null) {
                if (TaskStatus.PENDING_SUBMISSION.getValue().equals(aiImageTaskQueuePo.getTaskStatus())) {
                    return ShotResourceStatus.NOT_EXIST.getValue();
                } else if (TaskStatus.PENDING.getValue().equals(aiImageTaskQueuePo.getTaskStatus())) {
                    return ShotResourceStatus.QUEUED.getValue();
                } else if (TaskStatus.PROCESSING.getValue().equals(aiImageTaskQueuePo.getTaskStatus())) {
                    return ShotResourceStatus.IN_PROGRESS.getValue();
                } else if (TaskStatus.COMPLETED.getValue().equals(aiImageTaskQueuePo.getTaskStatus())) {
                    return ShotResourceStatus.COMPLETED.getValue();
                } else if (TaskStatus.FAILED.getValue().equals(aiImageTaskQueuePo.getTaskStatus())) {
                    return ShotResourceStatus.NOT_EXIST.getValue();
                }
            } else {
                // 只有在数据库中没有找到记录时，才使用其他判断逻辑
                
                // 检查是否有显式的状态
                if (StringUtils.isNotBlank(resourceStatus)) {
                    if (TaskStatus.PENDING_SUBMISSION.getValue().equals(resourceStatus)) {
                        return ShotResourceStatus.NOT_EXIST.getValue();
                    } else if (TaskStatus.PENDING.getValue().equals(resourceStatus)) {
                        return ShotResourceStatus.QUEUED.getValue();
                    } else if (TaskStatus.PROCESSING.getValue().equals(resourceStatus)) {
                        return ShotResourceStatus.IN_PROGRESS.getValue();
                    } else if (TaskStatus.COMPLETED.getValue().equals(resourceStatus)) {
                        return ShotResourceStatus.COMPLETED.getValue();
                    } else if (TaskStatus.FAILED.getValue().equals(resourceStatus)) {
                        return ShotResourceStatus.NOT_EXIST.getValue();
                    }
                }
                
                // 如果资源URL不为空，根据URL判断
                if (StringUtils.isNotBlank(resourceUrl) && (resourceUrl.toLowerCase().endsWith(fileExtension))) {
                    return ShotResourceStatus.COMPLETED.getValue();
                }
            }
        } else if ("voice".equals(resourceType)) {
            // 对于音频资源，也优先查询任务表获取状态
            // 查询音频任务
            AiTtsRecordPo aiTtsRecordPo = aiTtsRecordMapper.selectOne(new LambdaQueryWrapper<AiTtsRecordPo>()
                    .eq(AiTtsRecordPo::getConversationId, conversationId)
                    .eq(AiTtsRecordPo::getContentId, contentId)
                    .orderByDesc(AiTtsRecordPo::getCreateTime)
                    .last("limit 1"));
            
            if (aiTtsRecordPo != null) {
                if (aiTtsRecordPo.getStatus() == 0) { // 0-待处理
                    return ShotResourceStatus.QUEUED.getValue();
                } else if (aiTtsRecordPo.getStatus() == 1) { // 1-成功
                    return ShotResourceStatus.COMPLETED.getValue();
                } else if (aiTtsRecordPo.getStatus() == 2) { // 2-失败
                    return ShotResourceStatus.NOT_EXIST.getValue();
                }
            } else {
                // 只有在数据库中没有找到记录时，才使用其他判断逻辑
                
                // 检查是否有显式的状态
                if (StringUtils.isNotBlank(resourceStatus)) {
                    if (TaskStatus.PENDING_SUBMISSION.getValue().equals(resourceStatus)) {
                        return ShotResourceStatus.NOT_EXIST.getValue();
                    } else if (TaskStatus.PENDING.getValue().equals(resourceStatus)) {
                        return ShotResourceStatus.QUEUED.getValue();
                    } else if (TaskStatus.PROCESSING.getValue().equals(resourceStatus)) {
                        return ShotResourceStatus.IN_PROGRESS.getValue();
                    } else if (TaskStatus.COMPLETED.getValue().equals(resourceStatus)) {
                        return ShotResourceStatus.COMPLETED.getValue();
                    } else if (TaskStatus.FAILED.getValue().equals(resourceStatus)) {
                        return ShotResourceStatus.NOT_EXIST.getValue();
                    }
                }
                
                // 如果资源URL不为空，根据URL判断
                if (StringUtils.isNotBlank(resourceUrl) && (resourceUrl.toLowerCase().endsWith(fileExtension))) {
                    return ShotResourceStatus.COMPLETED.getValue();
                }
            }
        }
        
        // 默认情况，资源不存在
        return ShotResourceStatus.NOT_EXIST.getValue();
    }

//    @Override
//    public ShotTaskStatusGroupRes getShotTaskStatusGrouped(String conversationId, String contentId, Integer contentIdType) {
//        log.info("查询分镜任务状态(分组): conversationId={}, contentId={}, contentIdType={}", conversationId, contentId, contentIdType);
//
//        // 查询分镜内容和基本验证逻辑保持不变
//        AiCreationSessionPo aiCreationSessionPo = getAiCreationSessionPo(conversationId);
//        final int contentType = 4; // 分镜内容类型固定为4
//
//        AiCreationContentPo aiCreationContentPo = getOne(new LambdaQueryWrapper<AiCreationContentPo>()
//                .eq(AiCreationContentPo::getSessionId, aiCreationSessionPo.getSessionId())
//                .eq(AiCreationContentPo::getContentType, contentType)
//                .eq(AiCreationContentPo::getDelFlag, 0)
//                .last("Limit 1"));
//
//        if (aiCreationContentPo == null || StringUtils.isBlank(aiCreationContentPo.getContentData())) {
//            log.warn("未找到分镜内容数据, sessionId: {}", aiCreationSessionPo.getSessionId());
//            return new ShotTaskStatusGroupRes();
//        }
//
//        // 解析分镜数据
//        ShotSaveReq shotSaveReq = JSON.parseObject(aiCreationContentPo.getContentData(), ShotSaveReq.class);
//
//        // 提取段落ID (segmentId)
//        // 从shotGroup中获取segmentId，如果不存在则使用contentId
//        String segmentId = contentId;
//        if (contentIdType == 1) {
//            // 如果是章节ID，直接使用
//            segmentId = contentId;
//        } else {
//            // 尝试从镜头组中提取segmentId
//            for (ShotSaveReq.ShotGroupsDTO shotGroup : shotSaveReq.getShotGroups()) {
//                if (contentIdType == 2 && contentId.equals(shotGroup.getSceneId())) {
//                    // 如果是场景ID，从其所在的组获取segmentId
//                    segmentId = shotGroup.getSegmentId();
//                    break;
//                } else if (contentIdType == 3) {
//                    // 如果是镜头ID，遍历查找对应的镜头
//                    for (ShotSaveReq.ShotGroupsDTO.ShotsDTO shot : shotGroup.getShots()) {
//                        if (contentId.equals(shot.getId())) {
//                            segmentId = shotGroup.getSegmentId();
//                            break;
//                        }
//                    }
//                    if (!contentId.equals(segmentId)) {
//                        // 找到了，不再继续查找
//                        break;
//                    }
//                }
//            }
//        }
//
//        // 按场景分组数据
//        Map<String, List<ShotStatusDetailDto>> sceneToShotsMap = new HashMap<>();
//
//        // 根据contentIdType不同，处理不同范围的数据
//        if (contentIdType == 1) {
//            // 章节ID，处理所有分镜
//            for (ShotSaveReq.ShotGroupsDTO shotGroup : shotSaveReq.getShotGroups()) {
//                // 如果段落ID相同
//                if (StringUtils.equals(contentId, shotGroup.getSegmentId())) {
//                    for (ShotSaveReq.ShotGroupsDTO.ShotsDTO shot : shotGroup.getShots()) {
//                        addShotToSceneMap(sceneToShotsMap, shotGroup.getSceneId(), shot, conversationId);
//                    }
//                }
//            }
//        } else if (contentIdType == 2) {
//            // 场景ID，处理指定场景的分镜
//            for (ShotSaveReq.ShotGroupsDTO shotGroup : shotSaveReq.getShotGroups()) {
//                if (contentId.equals(shotGroup.getSceneId())) {
//                    for (ShotSaveReq.ShotGroupsDTO.ShotsDTO shot : shotGroup.getShots()) {
//                        addShotToSceneMap(sceneToShotsMap, shotGroup.getSceneId(), shot, conversationId);
//                    }
//                }
//            }
//        } else if (contentIdType == 3) {
//            // 镜头ID，只处理指定镜头
//            for (ShotSaveReq.ShotGroupsDTO shotGroup : shotSaveReq.getShotGroups()) {
//                for (ShotSaveReq.ShotGroupsDTO.ShotsDTO shot : shotGroup.getShots()) {
//                    if (contentId.equals(shot.getId())) {
//                        addShotToSceneMap(sceneToShotsMap, shotGroup.getSceneId(), shot, conversationId);
//                        break;
//                    }
//                }
//            }
//        }
//
//        // 构建场景列表
//        List<SceneStatusGroupDto> sceneGroups = new ArrayList<>();
//        for (Map.Entry<String, List<ShotStatusDetailDto>> entry : sceneToShotsMap.entrySet()) {
//            SceneStatusGroupDto sceneGroup = SceneStatusGroupDto.builder()
//                    .sceneId(entry.getKey())
//                    .shots(entry.getValue())
//                    .build();
//            sceneGroups.add(sceneGroup);
//        }
//
//        // 构建并返回最终响应
//        return ShotTaskStatusGroupRes.builder()
//                .chapterID(segmentId)
//                .scenes(sceneGroups)
//                .build();
//    }
//
//    /**
//     * 将镜头添加到场景映射中
//     */
//    private void addShotToSceneMap(Map<String, List<ShotStatusDetailDto>> sceneToShotsMap,
//                                   String sceneId,
//                                   ShotSaveReq.ShotGroupsDTO.ShotsDTO shot,
//                                   String conversationId) {
//        log.debug("处理镜头: shotId={}, sceneId={}", shot.getId(), sceneId);
//
//        // 检查图片、语音和旁白状态
//        String imageStatus = checkResourceStatus(shot.getImage(), shot.getImageStatus(), ".png", conversationId, shot.getId(), "image");
//        String voiceStatus = checkResourceStatus(shot.getVoice(), null, ".wav", conversationId, shot.getId(), "voice");
//        String narrationStatus = StringUtils.isNotBlank(shot.getNarration()) ?
//                ShotResourceStatus.COMPLETED.getValue() : ShotResourceStatus.NOT_EXIST.getValue();
//
//        log.debug("镜头状态: shotId={}, image={}, voice={}, narration={}",
//                shot.getId(), imageStatus, voiceStatus, narrationStatus);
//
//        // 创建镜头状态详情
//        ShotStatusDetailDto shotDetail = ShotStatusDetailDto.builder()
//                .shotId(shot.getId())
//                .imageStatus(imageStatus)
//                .voiceStatus(voiceStatus)
//                .narrationStatus(narrationStatus)
//                .build();
//
//        // 添加到对应场景的列表中
//        sceneToShotsMap.computeIfAbsent(sceneId, k -> new ArrayList<>()).add(shotDetail);
//    }

    @Override
    public ShotTaskStatusGroupsRes getShotTaskStatus(String conversationId, String contentId, Integer contentIdType) {
        log.info("查询分镜任务状态: conversationId={}, contentId={}, contentIdType={}", conversationId, contentId, contentIdType);
        
        // 首先获取故事大纲数据 (contentType=1)
        AiCreationSessionPo aiCreationSessionPo = getAiCreationSessionPo(conversationId);
        
        // 查询故事大纲数据
        AiCreationContentPo storyContentPo = getOne(new LambdaQueryWrapper<AiCreationContentPo>()
                .eq(AiCreationContentPo::getSessionId, aiCreationSessionPo.getSessionId())
                .eq(AiCreationContentPo::getContentType, 1) // 故事内容类型为1
                .eq(AiCreationContentPo::getDelFlag, 0)
                .last("Limit 1"));
        
        // 如果没有故事大纲，直接返回空响应
        if (storyContentPo == null || StringUtils.isBlank(storyContentPo.getContentData())) {
            log.warn("未找到故事大纲数据, sessionId: {}", aiCreationSessionPo.getSessionId());
            return new ShotTaskStatusGroupsRes();
        }
        
        // 解析故事大纲数据
        StorySaveReq storySaveReq = JSON.parseObject(storyContentPo.getContentData(), StorySaveReq.class);
        if (storySaveReq == null || CollUtil.isEmpty(storySaveReq.getChapters())) {
            log.warn("故事大纲数据为空, sessionId: {}", aiCreationSessionPo.getSessionId());
            return new ShotTaskStatusGroupsRes();
        }
        
        // 查询分镜数据 - 从新表结构中获取
        final int shotContentType = 4; // 分镜内容类型固定为4
        
        // 查询分镜数据 - 从新表结构中获取
        // 查询章节数据
        List<AiChapterPo> chapters = aiChapterMapper.selectList(
                new LambdaQueryWrapper<AiChapterPo>()
                        .eq(AiChapterPo::getSessionId, conversationId)
                        .eq(AiChapterPo::getDelFlag, 0)
        );
        
        // 查询所有分镜数据
        List<AiShotPo> allShots = aiShotMapper.selectList(
                new LambdaQueryWrapper<AiShotPo>()
                        .eq(AiShotPo::getSessionId, conversationId)
                        .eq(AiShotPo::getDelFlag, 0)
        );
        
        // 构建分镜数据对象，与原代码兼容
        ShotSaveReq shotSaveReq = null;
        
        if (CollUtil.isNotEmpty(chapters) && CollUtil.isNotEmpty(allShots)) {
            // 按章节ID和场景ID分组
            Map<String, Map<String, List<AiShotPo>>> chapterSceneMap = new HashMap<>();
            for (AiShotPo shot : allShots) {
                String segmentId = shot.getSegmentId();
                String sceneId = shot.getSceneId();
                
                // 按章节分组
                if (!chapterSceneMap.containsKey(segmentId)) {
                    chapterSceneMap.put(segmentId, new HashMap<>());
                }
                
                // 按场景分组
                Map<String, List<AiShotPo>> sceneMap = chapterSceneMap.get(segmentId);
                if (!sceneMap.containsKey(sceneId)) {
                    sceneMap.put(sceneId, new ArrayList<>());
                }
                
                sceneMap.get(sceneId).add(shot);
            }
            
            // 构建ShotSaveReq对象
            shotSaveReq = new ShotSaveReq();
            List<ShotSaveReq.ShotGroupsDTO> shotGroups = new ArrayList<>();
            
            for (AiChapterPo chapter : chapters) {
                Map<String, List<AiShotPo>> sceneMap = chapterSceneMap.getOrDefault(chapter.getSegmentId(), Collections.emptyMap());
                
                for (Map.Entry<String, List<AiShotPo>> entry : sceneMap.entrySet()) {
                    String sceneId = entry.getKey();
                    List<AiShotPo> sceneShots = entry.getValue();
                    
                    if (sceneShots.isEmpty()) {
                        continue;
                    }
                    
                    ShotSaveReq.ShotGroupsDTO shotGroup = new ShotSaveReq.ShotGroupsDTO();
                    shotGroup.setSceneId(sceneId);
                    shotGroup.setSceneName(sceneShots.get(0).getSceneName());
                    shotGroup.setSegmentId(chapter.getSegmentId());
                    shotGroup.setSegmentName(chapter.getSegmentName());
                    shotGroup.setTotalShots(sceneShots.size());
                    
                    List<ShotSaveReq.ShotGroupsDTO.ShotsDTO> shotList = new ArrayList<>();
                    
                    for (AiShotPo shot : sceneShots) {
                        ShotSaveReq.ShotGroupsDTO.ShotsDTO shotDTO = JSON.parseObject(
                                shot.getShotData(), 
                                ShotSaveReq.ShotGroupsDTO.ShotsDTO.class
                        );
                        shotList.add(shotDTO);
                    }
                    
                    shotGroup.setShots(shotList);
                    shotGroups.add(shotGroup);
                }
            }
            
            shotSaveReq.setShotGroups(shotGroups);
        } else {
            // 如果新表中没有数据，查询旧表作为向后兼容处理
            AiCreationContentPo shotContentPo = getOne(new LambdaQueryWrapper<AiCreationContentPo>()
                    .eq(AiCreationContentPo::getSessionId, aiCreationSessionPo.getSessionId())
                    .eq(AiCreationContentPo::getContentType, shotContentType)
                    .eq(AiCreationContentPo::getDelFlag, 0)
                    .last("Limit 1"));
            
            if (shotContentPo != null && StringUtils.isNotBlank(shotContentPo.getContentData())) {
                shotSaveReq = JSON.parseObject(shotContentPo.getContentData(), ShotSaveReq.class);
            }
        }
        
        // 构建返回结果
        List<ShotTaskStatusGroupsRes.ChapterGroup> chapterGroups = new ArrayList<>();
        
        // 从故事大纲中提取章节信息并初始化结构
        for (StorySaveReq.ChaptersDTO chapter : storySaveReq.getChapters()) {
            // 如果contentId不为空且是章节筛选，则只处理匹配的章节
            if (StringUtils.isNotBlank(contentId) && contentIdType == 1 && !contentId.equals(chapter.getChapterID())) {
                continue;
            }
            
            List<ShotTaskStatusGroupsRes.SceneGroup> sceneGroups = new ArrayList<>();
            boolean allScenesCompleted = true; // 章节状态标志
            
            // 处理章节下的场景
            if (CollUtil.isNotEmpty(chapter.getScenes())) {
                for (StorySaveReq.ChaptersDTO.ScenesDTO scene : chapter.getScenes()) {
                    // 如果contentId不为空且是场景筛选，则只处理匹配的场景
                    if (StringUtils.isNotBlank(contentId) && contentIdType == 2 && !contentId.equals(scene.getId())) {
                        continue;
                    }
                    List<ShotTaskStatusGroupsRes.ShotDetail> shotDetails = new ArrayList<>();
                    boolean allShotsCompleted = true; // 场景状态标志
                    int shotCount = scene.getShots().size(); // 镜头数量始终以故事场景中的镜头数为准
                    
                    // 获取场景中所有镜头ID的映射，用于检查是否所有镜头都在分镜数据中有对应实现
                    Map<Integer, Boolean> storyShots = new HashMap<>();
                    for (StorySaveReq.ChaptersDTO.ScenesDTO.ShotsDTO storyShot : scene.getShots()) {
                        storyShots.put(storyShot.getShot(), false); // 初始化为未实现
                    }
                    
                    // 如果有分镜数据，查找对应的镜头状态
                    if (shotSaveReq != null && CollUtil.isNotEmpty(shotSaveReq.getShotGroups())) {
                        // 查找对应场景的分镜组
                        for (ShotSaveReq.ShotGroupsDTO shotGroup : shotSaveReq.getShotGroups()) {
                            if (scene.getId().equals(shotGroup.getSceneId())) {
                                // 不再从分镜数据中获取镜头数量
                                // shotCount = shotGroup.getShots().size();
                                
                                // 处理场景下的所有镜头
                                for (ShotSaveReq.ShotGroupsDTO.ShotsDTO shot : shotGroup.getShots()) {
                                    // 如果contentId不为空且是镜头筛选，则只处理匹配的镜头
                                    if (StringUtils.isNotBlank(contentId) && contentIdType == 3 && !contentId.equals(shot.getId())) {
                                        continue;
                                    }
                                    
                                    // 检查镜头的各种资源状态
                                    String imageStatus = checkResourceStatus(shot.getImage(), shot.getImageStatus(), ".png", conversationId, shot.getId(), "image");
                                    String voiceStatus = checkResourceStatus(shot.getVoice(), null, ".wav", conversationId, shot.getId(), "voice");
                                    String narrationStatus = StringUtils.isNotBlank(shot.getNarration()) ? 
                                            ShotResourceStatus.COMPLETED.getValue() : ShotResourceStatus.NOT_EXIST.getValue();
                                    
                                    // 如果任一资源未完成，则场景状态为未完成
                                    if (!ShotResourceStatus.COMPLETED.getValue().equals(imageStatus) || 
                                        !ShotResourceStatus.COMPLETED.getValue().equals(voiceStatus) || 
                                        !ShotResourceStatus.COMPLETED.getValue().equals(narrationStatus)) {
                                        allShotsCompleted = false;
                                    }
                                    
                                    // 创建镜头状态详情
                                    ShotTaskStatusGroupsRes.ShotDetail shotDetail = ShotTaskStatusGroupsRes.ShotDetail.builder()
                                            .shotId(shot.getId())
                                            .imageStatus(imageStatus)
                                            .voiceStatus(voiceStatus)
                                            .narrationStatus(narrationStatus)
                                            .build();
                                    
                                    shotDetails.add(shotDetail);
                                    
                                    // 尝试标记故事中对应的镜头为已实现（通过ID的后缀数字匹配）
                                    try {
                                        String idStr = shot.getId();
                                        String numStr = idStr.substring(idStr.lastIndexOf("-") + 1);
                                        int shotNum = Integer.parseInt(numStr);
                                        if (storyShots.containsKey(shotNum)) {
                                            storyShots.put(shotNum, true);
                                        }
                                    } catch (Exception e) {
                                        log.warn("无法从镜头ID中提取编号: {}", shot.getId());
                                    }
                                }
                                
                                break; // 找到对应场景后跳出循环
                            }
                        }
                    }
                    
                    // 检查是否所有故事中的镜头都在分镜中有实现
                    for (Map.Entry<Integer, Boolean> entry : storyShots.entrySet()) {
                        if (!entry.getValue()) {
                            allShotsCompleted = false;
                            break;
                        }
                    }
                    
                    // 如果场景下没有镜头或有镜头未完成，则章节状态为未完成
                    if (shotDetails.isEmpty() || !allShotsCompleted) {
                        allScenesCompleted = false;
                    }
                    
                    // 创建场景组
                    ShotTaskStatusGroupsRes.SceneGroup sceneGroup = ShotTaskStatusGroupsRes.SceneGroup.builder()
                            .sceneId(scene.getId())
                            .shotStatus(allShotsCompleted ? "已完成" : "未完成")
                            .shotCount(String.valueOf(shotCount))
                            .shots(shotDetails)
                            .build();
                    
                    sceneGroups.add(sceneGroup);
                }
            } else {
                // 如果章节下没有场景，章节状态为未完成
                allScenesCompleted = false;
            }
            
            // 创建章节组
            ShotTaskStatusGroupsRes.ChapterGroup chapterGroup = ShotTaskStatusGroupsRes.ChapterGroup.builder()
                    .chapterID(chapter.getChapterID())
                    .chapterStatus(allScenesCompleted ? "已完成" : "未完成")
                    .scenes(sceneGroups)
                    .build();
            
            chapterGroups.add(chapterGroup);
        }
        
        // 返回最终结果
        return ShotTaskStatusGroupsRes.builder()
                .shotGroups(chapterGroups)
                .build();
    }
}