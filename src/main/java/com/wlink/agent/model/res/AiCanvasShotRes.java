package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 画布分镜响应
 */
@Data
@Schema(description = "画布分镜响应")
public class AiCanvasShotRes {
    
    /**
     * 分镜ID
     */
    @Schema(description = "分镜ID")
    private Long id;
    
    /**
     * 画布ID
     */
    @Schema(description = "画布ID")
    private Long canvasId;
    
    /**
     * 分镜编码，如C01-SC-02-2
     */
    @Schema(description = "分镜编码")
    private String code;
    
    /**
     * 原始分镜ID，转换时有值
     */
    @Schema(description = "原始分镜ID")
    private String originalShotId;
    
    /**
     * 分镜类型
     */
    @Schema(description = "分镜类型")
    private String type;

    /**
     * 显示类型
     */
    @Schema(description = "显示类型")
    private String displayType;

    /**
     * 构图描述
     */
    @Schema(description = "构图描述")
    private String composition;
    
    /**
     * 运动类型
     */
    @Schema(description = "运动类型")
    private String movement;
    
    /**
     * 排序序号
     */
    @Schema(description = "排序序号")
    private Integer sortOrder;

    /**
     * 分镜状态(0-初始,1-处理中,2-已完成)
     */
    @Schema(description = "分镜状态(0-初始,1-处理中,2-已完成)")
    private Integer shotStatus;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
    
    /**
     * 图片URL
     */
    @Schema(description = "图片URL")
    private String imageUrl;
    
    /**
     * 图片生成提示词
     */
    @Schema(description = "图片生成提示词")
    private String imagePrompt;
    
    /**
     * 图片描述
     */
    @Schema(description = "图片描述")
    private String imageDesc;
    
    /**
     * 图片宽高比
     */
    @Schema(description = "图片宽高比")
    private String imageAspectRatio;
    
    /**
     * 图片状态
     */
    @Schema(description = "图片状态")
    private String imageStatus;
    
    /**
     * 参考图片URL
     */
    @Schema(description = "参考图片URL")
    private String referenceImage;


    /**
     * 视频转换提示词
     */
    @Schema(description = "视频转换提示词")
    private String videoConvertPrompt;


    /**
     * 视频URL
     */
    @Schema(description = "视频URL")
    private String videoUrl;
    
    /**
     * 视频生成提示词
     */
    @Schema(description = "视频生成提示词")
    private String videoPrompt;
    
    /**
     * 视频描述
     */
    @Schema(description = "视频描述")
    private String videoDesc;
    
    /**
     * 视频时长(毫秒)
     */
    @Schema(description = "视频时长(毫秒)")
    private Integer videoDuration;
    
    /**
     * 视频宽高比
     */
    @Schema(description = "视频宽高比")
    private String videoAspectRatio;
    
    /**
     * 视频状态
     */
    @Schema(description = "视频状态")
    private String videoStatus;
    
    /**
     * 开始帧图片URL
     */
    @Schema(description = "开始帧图片URL")
    private String startFrameImage;
    
    /**
     * 结束帧图片URL
     */
    @Schema(description = "结束帧图片URL")
    private String endFrameImage;
    
    /**
     * 音频资源列表
     */
    @Schema(description = "音频资源列表")
    private List<AiCanvasAudioRes> audios;
} 