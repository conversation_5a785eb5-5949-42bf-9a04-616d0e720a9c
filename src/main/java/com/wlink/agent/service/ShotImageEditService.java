package com.wlink.agent.service;

import com.wlink.agent.model.req.ShotImageEditReq;
import com.wlink.agent.model.res.ShotImageEditRes;

import java.util.List;

/**
 * 分镜图片编辑服务接口
 */
public interface ShotImageEditService {

    /**
     * 提交分镜图片编辑任务
     *
     * @param req 编辑请求
     * @return 编辑响应
     */
    ShotImageEditRes submitImageEdit(ShotImageEditReq req);

    /**
     * 根据任务ID查询编辑记录
     *
     * @param taskId 任务ID
     * @return 编辑记录
     */
    ShotImageEditRes getEditRecordByTaskId(String taskId);

    /**
     * 根据分镜ID查询编辑记录列表
     *
     * @param shotId 分镜ID
     * @return 编辑记录列表
     */
    List<ShotImageEditRes> getEditRecordsByShotId(Long shotId);

    /**
     * 更新编辑记录状态
     *
     * @param taskId 任务ID
     * @param status 状态
     * @param resultImageUrl 结果图片URL
     * @param taskCostTime 任务耗时
     */
    void updateEditRecordStatus(String taskId, String status, String resultImageUrl, Long taskCostTime);

    /**
     * 更新编辑记录为失败状态
     *
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     */
    void updateEditRecordToFailed(String taskId, String errorMessage);
}
