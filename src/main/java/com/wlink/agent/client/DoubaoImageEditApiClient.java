package com.wlink.agent.client;


import com.alibaba.cola.exception.BizException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.client.model.doubao.DoubaoImageEditRequest;
import com.wlink.agent.client.model.doubao.DoubaoImageEditResponse;


import com.wlink.agent.client.model.volcengine.ImageGenerateRes;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 豆包图像编辑API客户端
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class DoubaoImageEditApiClient {
    
    private static final String DEFAULT_API_URL = "https://ark.cn-beijing.volces.com/api/v3/images/generations";
    private static final MediaType JSON_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");
    
    @Resource
    private ObjectMapper objectMapper;
    
    @Resource
    private OkHttpClient okHttpClient;
    
    @Value("${doubao.image.edit.api.url:" + DEFAULT_API_URL + "}")
    private String apiUrl;
    
    @Value("${doubao.image.edit.api.key:0c951ad9-b5db-4cbf-a556-3bdf04f4bace}")
    private String defaultApiKey;
    
    /**
     * 使用默认API密钥编辑图像
     * 
     * @param request 图像编辑请求
     * @return 图像编辑结果
     */
    public ImageGenerateRes editImage(DoubaoImageEditRequest request) {
        return editImageWithApiKey(request, defaultApiKey);
    }
    
    /**
     * 使用指定API密钥编辑图像
     * 
     * @param request 图像编辑请求
     * @param apiKey API密钥
     * @return 图像编辑结果
     */
    public ImageGenerateRes editImageWithApiKey(DoubaoImageEditRequest request, String apiKey) {
        try {
            // 参数验证
            if (request == null) {
                throw new BizException("请求参数不能为空");
            }
            if (apiKey == null || apiKey.trim().isEmpty()) {
                throw new BizException("API密钥不能为空");
            }
            
            // 构建HTTP请求
            Request httpRequest = buildRequest(request, apiKey);
            
            // 执行HTTP调用
            String responseBody = executeRequest(httpRequest);
            
            // 尝试解析错误响应
            ImageGenerateRes errorResponse = parseErrorResponse(responseBody);
            if (errorResponse != null) {
                return errorResponse;
            }
            
            // 解析正常响应
            return parseResponse(responseBody);
            
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("调用豆包图像编辑API发生异常", e);
            throw new BizException("豆包图像编辑API调用失败: " + e.getMessage());
        }
    }
    
    /**
     * 构建HTTP请求
     */
    private Request buildRequest(DoubaoImageEditRequest request, String apiKey) throws Exception {
        // 序列化请求体
        String requestBodyJson = objectMapper.writeValueAsString(request);
        byte[] bodyBytes = requestBodyJson.getBytes(StandardCharsets.UTF_8);
        
        log.info("调用豆包图像编辑API, URL: {}, 请求: {}", apiUrl, requestBodyJson);
        
        // 构建请求
        return new Request.Builder()
                .url(apiUrl)
                .post(RequestBody.create(bodyBytes, JSON_MEDIA_TYPE))
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + apiKey)
                .build();
    }
    
    /**
     * 执行HTTP请求
     */
    private String executeRequest(Request request) throws IOException {
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "无响应体";
                log.error("豆包图像编辑API调用失败, HTTP状态码: {}, 响应: {}", response.code(), errorBody);
                throw new BizException("豆包图像编辑API调用失败，HTTP状态码: " + response.code());
            }
            
            String responseBody = response.body() != null ? response.body().string() : "";
            log.info("豆包图像编辑API调用成功, 响应长度: {}", responseBody.length());
            return responseBody;
        }
    }
    
    /**
     * 尝试解析错误响应
     */
    private ImageGenerateRes parseErrorResponse(String responseBody) {
        try {
            DoubaoImageEditResponse response = objectMapper.readValue(responseBody, DoubaoImageEditResponse.class);
            if (response.getError() != null) {
                log.error("豆包图像编辑API返回错误: code={}, message={}", 
                         response.getError().getCode(), response.getError().getMessage());
                return new ImageGenerateRes(null, response.getError().getCode(), response.getError().getMessage());
            }
        } catch (Exception e) {
            // 如果不是错误响应格式，继续处理
            log.debug("响应不是错误格式，继续解析正常响应");
        }
        return null;
    }
    
    /**
     * 解析正常响应
     */
    private ImageGenerateRes parseResponse(String responseBody) throws Exception {
        DoubaoImageEditResponse response = objectMapper.readValue(responseBody, DoubaoImageEditResponse.class);

        if (response.getData() == null || response.getData().isEmpty()) {
            throw new BizException("豆包图像编辑API返回数据为空");
        }

        // 获取第一张图片的URL
        String imageUrl = response.getData().get(0).getUrl();
        if (imageUrl == null || imageUrl.trim().isEmpty()) {
            throw new BizException("豆包图像编辑API返回的图片URL为空");
        }

        log.info("豆包图像编辑成功, 图片URL: {}", imageUrl);

        // 构建响应 - 使用volcengine包下的ImageGenerateRes
        return new ImageGenerateRes(imageUrl);
    }
}
