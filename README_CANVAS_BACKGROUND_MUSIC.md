# 画布背景音乐功能

## 功能概述

为画布系统新增了背景音乐功能，支持为每个画布设置一个背景音乐。该功能包括新增、更新、查询和删除背景音乐的完整接口。

## 数据库设计

### 表结构：ai_canvas_background_music

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint(20) | 主键ID |
| canvas_id | bigint(20) | 画布ID（唯一约束） |
| audio_url | varchar(500) | 音频地址 |
| audio_name | varchar(255) | 音频名称 |
| audio_duration | bigint(20) | 音频时长（毫秒） |
| start_play_time | bigint(20) | 开始播放时间（毫秒） |
| end_play_time | bigint(20) | 结束播放时间（毫秒） |
| start_track_time | bigint(20) | 音轨开始时间（毫秒） |
| volume | int(11) | 音量（0-100） |
| fade_in_time | bigint(20) | 淡入时间（毫秒） |
| fade_out_time | bigint(20) | 淡出时间（毫秒） |
| is_loop | tinyint(1) | 是否循环播放（0-否，1-是） |
| audio_format | varchar(20) | 音频格式 |
| file_size | bigint(20) | 音频文件大小（字节） |
| audio_source | tinyint(1) | 音频来源（1-上传，2-AI生成，3-素材库） |
| description | varchar(1000) | 音频描述 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |
| del_flag | tinyint(1) | 删除标记（0-正常，1-删除） |

### 约束条件

- `canvas_id` 字段有唯一约束，确保一个画布只能有一个背景音乐
- 使用逻辑删除，通过 `del_flag` 字段标记删除状态

## API 接口

### 1. 设置画布背景音乐

**接口地址：** `POST /agent/canvas/background-music/set`

**功能说明：** 新增或更新画布背景音乐，一个画布只能有一个背景音乐。当音频URL为空时，会删除背景音乐

**请求参数：**
```json
{
  "canvasId": 123456789,
  "audioUrl": "https://example.com/background.mp3",
  "audioName": "背景音乐.mp3",
  "audioDuration": 180000,
  "startPlayTime": 0,
  "endPlayTime": 180000,
  "startTrackTime": 0,
  "volume": 80,
  "fadeInTime": 2000,
  "fadeOutTime": 2000,
  "isLoop": 1,
  "audioFormat": "mp3",
  "fileSize": 5242880,
  "audioSource": 1,
  "description": "轻松愉快的背景音乐"
}
```

**删除背景音乐的请求参数：**
```json
{
  "canvasId": 123456789,
  "audioUrl": null
}
```
或
```json
{
  "canvasId": 123456789,
  "audioUrl": ""
}
```

**响应结果：**
```json
{
  "success": true,
  "data": 987654321,
  "errorCode": null,
  "errorMessage": null
}
```

### 2. 获取画布背景音乐

**接口地址：** `GET /agent/canvas/background-music/{canvasId}`

**功能说明：** 根据画布ID获取背景音乐信息

**注意：** 画布详情接口 `GET /agent/canvas/detail/{canvasId}` 也会返回背景音乐信息

**响应结果：**
```json
{
  "success": true,
  "data": {
    "id": 987654321,
    "canvasId": 123456789,
    "audioUrl": "https://example.com/background.mp3",
    "audioName": "背景音乐.mp3",
    "audioDuration": 180000,
    "audioDurationDesc": "3分0秒",
    "startPlayTime": 0,
    "endPlayTime": 180000,
    "startTrackTime": 0,
    "volume": 80,
    "fadeInTime": 2000,
    "fadeOutTime": 2000,
    "isLoop": 1,
    "isLoopDesc": "是",
    "audioFormat": "mp3",
    "fileSize": 5242880,
    "fileSizeDesc": "5.0 MB",
    "audioSource": 1,
    "audioSourceDesc": "上传",
    "description": "轻松愉快的背景音乐",
    "createTime": "2025-07-21T10:00:00",
    "updateTime": "2025-07-21T10:00:00"
  },
  "errorCode": null,
  "errorMessage": null
}
```

### 3. 更新画布背景音乐

**接口地址：** `PUT /agent/canvas/background-music/{canvasId}`

**功能说明：** 更新指定画布的背景音乐信息

**请求参数：** 同设置接口

**响应结果：**
```json
{
  "success": true,
  "data": null,
  "errorCode": null,
  "errorMessage": null
}
```

### 4. 删除画布背景音乐

**接口地址：** `DELETE /agent/canvas/background-music/{canvasId}`

**功能说明：** 删除指定画布的背景音乐

**响应结果：**
```json
{
  "success": true,
  "data": null,
  "errorCode": null,
  "errorMessage": null
}
```

## 业务逻辑

### 设置背景音乐逻辑

1. **检查音频URL**：如果音频URL为空（null或空字符串），则删除背景音乐
2. **删除逻辑**：当URL为空时，查找现有背景音乐并执行逻辑删除
3. **更新逻辑**：如果画布已存在背景音乐且URL不为空，则更新现有记录
4. **创建逻辑**：如果画布不存在背景音乐且URL不为空，则创建新记录
5. **唯一约束**：通过数据库唯一约束确保一个画布只能有一个背景音乐

### 数据清理逻辑

在以下场景中会自动清理背景音乐数据：

1. **删除画布时：** 同时删除该画布的背景音乐
2. **清理画布数据时：** 在 `clearExistingCanvasData` 方法中清理背景音乐

### 响应数据格式化

响应数据包含多个格式化字段：

- `audioDurationDesc`：格式化的音频时长（如："3分0秒"）
- `fileSizeDesc`：格式化的文件大小（如："5.0 MB"）
- `isLoopDesc`：循环播放描述（"是"/"否"）
- `audioSourceDesc`：音频来源描述（"上传"/"AI生成"/"素材库"）

## 技术实现

### 核心文件

1. **数据库迁移文件：** `V20250721__create_canvas_background_music_table.sql`
2. **实体类：** `AiCanvasBackgroundMusicPo.java`
3. **Mapper接口：** `AiCanvasBackgroundMusicMapper.java`
4. **Mapper XML：** `AiCanvasBackgroundMusicMapper.xml`
5. **请求模型：** `CanvasBackgroundMusicReq.java`
6. **响应模型：** `CanvasBackgroundMusicRes.java`
7. **服务接口：** `CanvasBackgroundMusicService.java`
8. **服务实现：** `CanvasBackgroundMusicServiceImpl.java`
9. **控制器：** `CanvasBackgroundMusicController.java`
10. **渲染数据DTO：** `CanvasRenderDataDto.BackgroundMusicRenderDataDto`

### 集成点

- 在 `AiCanvasServiceImpl` 中集成了背景音乐的清理逻辑
- **在画布详情接口中集成了背景音乐信息**
- **在视频渲染导出数据中集成了背景音乐信息**
- 支持事务管理，确保数据一致性
- 使用 MyBatis-Plus 的逻辑删除功能

## 使用示例

### 设置背景音乐

```bash
curl -X POST "http://localhost:8080/agent/canvas/background-music/set" \
  -H "Content-Type: application/json" \
  -d '{
    "canvasId": 123456789,
    "audioUrl": "https://example.com/background.mp3",
    "audioName": "背景音乐.mp3",
    "audioDuration": 180000,
    "volume": 80,
    "isLoop": 1,
    "audioSource": 1,
    "description": "轻松愉快的背景音乐"
  }'
```

### 删除背景音乐（通过设置空URL）

```bash
curl -X POST "http://localhost:8080/agent/canvas/background-music/set" \
  -H "Content-Type: application/json" \
  -d '{
    "canvasId": 123456789,
    "audioUrl": null
  }'
```

### 获取背景音乐

```bash
curl -X GET "http://localhost:8080/agent/canvas/background-music/123456789"
```

### 删除背景音乐

```bash
curl -X DELETE "http://localhost:8080/agent/canvas/background-music/123456789"
```

### 获取画布详情（包含背景音乐）

```bash
curl -X GET "http://localhost:8080/agent/canvas/detail/123456789"
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "id": 123456789,
    "code": "CANVAS_001",
    "userId": "user123",
    "canvasName": "我的画布",
    "canvasDesc": "画布描述",
    "shots": [...],
    "backgroundMusic": {
      "id": 987654321,
      "canvasId": 123456789,
      "audioUrl": "https://example.com/background.mp3",
      "audioName": "背景音乐.mp3",
      "audioDuration": 180000,
      "audioDurationDesc": "3分0秒",
      "volume": 80,
      "isLoop": 1,
      "isLoopDesc": "是",
      "audioSourceDesc": "上传"
    }
  }
}
```

## 注意事项

1. 一个画布只能设置一个背景音乐
2. 设置新的背景音乐会覆盖原有的背景音乐
3. **当音频URL为空（null或空字符串）时，会删除背景音乐**
4. 删除画布时会自动删除关联的背景音乐
5. 所有时间相关字段均以毫秒为单位
6. 音量范围为 0-100
7. 支持多种音频格式（mp3、wav、ogg等）
8. 删除背景音乐有两种方式：通过设置空URL或直接调用删除接口
9. **视频渲染导出时会自动包含背景音乐数据**

## 视频渲染导出集成

### 功能说明

在视频渲染导出功能中，背景音乐数据会自动包含在发送给Python渲染服务的JSON数据中。

### 渲染数据结构

```json
{
  "canvasId": 123456789,
  "canvasName": "测试画布",
  "resolution": "1920x1080",
  "ratio": "16:9",
  "showSubtitle": 1,
  "fps": 24,
  "shots": [...],
  "backgroundMusic": {
    "audioUrl": "https://example.com/background.mp3",
    "audioName": "背景音乐.mp3",
    "audioDuration": 180000,
    "startPlayTime": 0,
    "endPlayTime": 180000,
    "startTrackTime": 0,
    "volume": 80,
    "fadeInTime": 2000,
    "fadeOutTime": 2000,
    "isLoop": 1,
    "audioFormat": "mp3",
    "audioSource": 1,
    "description": "轻松愉快的背景音乐"
  }
}
```

### 处理逻辑

1. **URL处理**：背景音乐URL通过 `MediaUrlPrefixUtil.getMediaUrl()` 方法处理为完整URL
2. **空值处理**：如果画布没有背景音乐，`backgroundMusic` 字段为null
3. **数据转换**：将 `CanvasBackgroundMusicRes` 转换为 `BackgroundMusicRenderDataDto`

### Python渲染服务注意事项

1. 需要处理 `backgroundMusic` 为null的情况
2. 音量范围为0-100，可能需要转换为0-1的范围
3. 循环播放标志(isLoop)为0或1，需要转换为布尔值
4. 时间相关字段均为毫秒单位

## 后续建议

1. 可以考虑添加背景音乐预览功能
2. 可以添加背景音乐素材库，方便用户选择
3. 可以考虑支持多个背景音乐的混合使用
4. 可以添加音频波形可视化功能
5. 可以在Python渲染服务中添加背景音乐的音频处理功能

这个实现满足了用户的需求，提供了完整的画布背景音乐管理功能，并且与视频渲染导出功能完美集成。
