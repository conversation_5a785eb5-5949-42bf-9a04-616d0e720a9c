package com.wlink.agent.event.listener;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wlink.agent.dao.mapper.AiCanvasMaterialMapper;
import com.wlink.agent.dao.po.AiCanvasMaterialPo;
import com.wlink.agent.enums.ResourceStatus;
import com.wlink.agent.utils.OssUtils;
import lombok.Data;
import com.wlink.agent.client.model.comfyui.ComfyUICallbackEventData;
import com.wlink.agent.client.model.comfyui.ComfyUICallbackRequest;
import com.wlink.agent.dao.mapper.AiCanvasImageMapper;
import com.wlink.agent.dao.mapper.AiCanvasShotMapper;
import com.wlink.agent.dao.mapper.AiShotImageEditMapper;
import com.wlink.agent.dao.po.AiCanvasImagePo;
import com.wlink.agent.dao.po.AiCanvasShotPo;
import com.wlink.agent.dao.po.AiShotImageEditPo;
import com.wlink.agent.enums.ShotStatus;
import com.wlink.agent.event.ComfyUICallbackEvent;
import com.wlink.agent.service.ShotImageEditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * ComfyUI 回调事件监听器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ComfyUICallbackListener {

    private final ShotImageEditService shotImageEditService;
    private final AiShotImageEditMapper aiShotImageEditMapper;
    private final AiCanvasImageMapper aiCanvasImageMapper;
    private final AiCanvasShotMapper aiCanvasShotMapper;
    private final AiCanvasMaterialMapper canvasMaterialMapper;
    private final OssUtils ossUtils;

    @Value("${spring.profiles.active}")
    String env;

    private static final String OSS_PATH = "dify/{env}/{userId}/{type}/";

    /**
     * 处理 ComfyUI 回调事件
     * 使用@Async注解实现异步处理
     * 
     * @param event ComfyUI 回调事件
     */
    @Async("taskExecutor")
    @EventListener
    public void handleComfyUICallback(ComfyUICallbackEvent event) {
        ComfyUICallbackRequest request = event.getRequest();
        log.info("异步处理 ComfyUI 回调事件: event={}, taskId={}", request.getEvent(), request.getTaskId());
        
        try {
            // 解析事件数据
            ComfyUICallbackEventData eventData = parseEventData(request.getEventData());
            
            if (eventData == null) {
                log.error("解析事件数据失败: taskId={}, eventData={}", request.getTaskId(), request.getEventData());
                return;
            }
            
            // 根据事件类型处理
            switch (request.getEvent()) {
                case "TASK_END" -> handleTaskEndEvent(request.getTaskId(), eventData);
                case "TASK_START" -> handleTaskStartEvent(request.getTaskId(), eventData);
                case "TASK_PROGRESS" -> handleTaskProgressEvent(request.getTaskId(), eventData);
                default -> log.warn("未处理的事件类型: event={}, taskId={}", request.getEvent(), request.getTaskId());
            }
            
        } catch (Exception e) {
            log.error("异步处理 ComfyUI 回调事件异常: event={}, taskId={}, error={}", 
                    request.getEvent(), request.getTaskId(), e.getMessage(), e);
        }
    }
    
    /**
     * 解析事件数据
     */
    private ComfyUICallbackEventData parseEventData(String eventDataJson) {
        try {
            return JSON.parseObject(eventDataJson, ComfyUICallbackEventData.class);
        } catch (Exception e) {
            log.error("解析事件数据异常: eventData={}", eventDataJson, e);
            return null;
        }
    }
    
    /**
     * 处理任务结束事件
     */
    private void handleTaskEndEvent(String taskId, ComfyUICallbackEventData eventData) {
        log.info("处理任务结束事件: taskId={}, code={}", taskId, eventData.getCode());

        if (eventData.getCode() != null && eventData.getCode() == 0) {
            // 任务成功完成
            List<ComfyUICallbackEventData.ComfyUIResultData> resultDataList = eventData.getResultDataList();
            log.info("任务成功完成: taskId={}, resultCount={}", taskId, resultDataList.size());

            for (ComfyUICallbackEventData.ComfyUIResultData resultData : resultDataList) {
                log.info("任务结果: taskId={}, nodeId={}, fileUrl={}, fileType={}, costTime={}ms",
                        taskId, resultData.getNodeId(), resultData.getFileUrl(),
                        resultData.getFileType(), resultData.getTaskCostTime());

                // 处理任务成功结果
                processTaskResult(taskId, resultData);
            }

            // 更新相关业务表状态
            if (!resultDataList.isEmpty()) {
                updateBusinessTablesOnSuccess(taskId, resultDataList.get(0));
            }

        } else {
            // 任务失败
            String errorMessage = buildErrorMessage(eventData);
            log.error("任务执行失败: taskId={}, code={}, msg={}, details={}",
                    taskId, eventData.getCode(), eventData.getMsg(), errorMessage);

            // 处理任务失败
            processTaskFailure(taskId, eventData);

            // 更新相关业务表状态
            updateBusinessTablesOnFailure(taskId, errorMessage);
        }
    }
    
    /**
     * 处理任务开始事件
     */
    private void handleTaskStartEvent(String taskId, ComfyUICallbackEventData eventData) {
        log.info("处理任务开始事件: taskId={}", taskId);
        
        // TODO: 添加任务开始的处理逻辑
        // 例如：更新任务状态为进行中
    }
    
    /**
     * 处理任务进度事件
     */
    private void handleTaskProgressEvent(String taskId, ComfyUICallbackEventData eventData) {
        log.info("处理任务进度事件: taskId={}", taskId);
        
        // TODO: 添加任务进度的处理逻辑
        // 例如：更新任务进度信息
    }
    
    /**
     * 处理任务结果
     */
    private void processTaskResult(String taskId, ComfyUICallbackEventData.ComfyUIResultData resultData) {
        log.info("处理任务结果: taskId={}, fileUrl={}", taskId, resultData.getFileUrl());

        try {
            // 更新分镜图片编辑记录状态
            shotImageEditService.updateEditRecordStatus(
                    taskId,
                    "COMPLETED",
                    resultData.getFileUrl(),
                    resultData.getTaskCostTime()
            );

            log.info("分镜图片编辑任务完成: taskId={}, resultUrl={}", taskId, resultData.getFileUrl());

        } catch (Exception e) {
            log.error("处理分镜图片编辑任务结果异常: taskId={}, error={}", taskId, e.getMessage(), e);
        }
    }
    
    /**
     * 处理任务失败
     */
    private void processTaskFailure(String taskId, ComfyUICallbackEventData eventData) {
        log.error("处理任务失败: taskId={}, error={}", taskId, eventData.getMsg());

        try {
            // 更新分镜图片编辑记录为失败状态
            shotImageEditService.updateEditRecordToFailed(taskId, eventData.getMsg());

            log.info("分镜图片编辑任务失败状态已更新: taskId={}", taskId);

        } catch (Exception e) {
            log.error("处理分镜图片编辑任务失败异常: taskId={}, error={}", taskId, e.getMessage(), e);
        }
    }

    /**
     * 任务成功时更新相关业务表
     */
    private void updateBusinessTablesOnSuccess(String taskId, ComfyUICallbackEventData.ComfyUIResultData resultData) {
        log.info("开始更新业务表状态 - 任务成功: taskId={}, fileUrl={}", taskId, resultData.getFileUrl());

        try {
            // 1. 查询分镜图片编辑记录
            LambdaQueryWrapper<AiShotImageEditPo> editQueryWrapper = new LambdaQueryWrapper<>();
            editQueryWrapper.eq(AiShotImageEditPo::getTaskId, taskId)
                    .eq(AiShotImageEditPo::getDelFlag, 0);

            AiShotImageEditPo editRecord = aiShotImageEditMapper.selectOne(editQueryWrapper);
            if (editRecord == null) {
                log.warn("未找到分镜图片编辑记录: taskId={}", taskId);
                return;
            }

            Long shotId = editRecord.getShotId();
            String resultImageUrl = resultData.getFileUrl();

            log.info("找到编辑记录: shotId={}, resultImageUrl={}", shotId, resultImageUrl);

            // 2. 根据shotId查询对应的画布分镜记录
            LambdaQueryWrapper<AiCanvasShotPo> shotQueryWrapper = new LambdaQueryWrapper<>();
            shotQueryWrapper.eq(AiCanvasShotPo::getId, shotId)
                    .eq(AiCanvasShotPo::getDelFlag, 0);

            AiCanvasShotPo canvasShotPo = aiCanvasShotMapper.selectOne(shotQueryWrapper);
            if (canvasShotPo != null) {
                // 3. 更新或创建画布图片记录
                updateCanvasImage(canvasShotPo, resultImageUrl, editRecord.getPrompt(),editRecord.getUserId());

                // 4. 更新画布分镜状态为已完成
                updateCanvasShotStatus(canvasShotPo.getId(), ShotStatus.COMPLETED.getValue()); // 2-已完成

                log.info("画布相关表更新成功: canvasId={}, shotCode={}",
                        canvasShotPo.getCanvasId(), canvasShotPo.getCode());
            } else {
                log.warn("未找到对应的画布分镜记录: originalShotId={}", shotId);
            }

        } catch (Exception e) {
            log.error("更新业务表状态异常 - 任务成功: taskId={}, error={}", taskId, e.getMessage(), e);
        }
    }

    /**
     * 任务失败时更新相关业务表
     */
    private void updateBusinessTablesOnFailure(String taskId, String errorMessage) {
        log.info("开始更新业务表状态 - 任务失败: taskId={}, error={}", taskId, errorMessage);

        try {
            // 1. 查询分镜图片编辑记录
            LambdaQueryWrapper<AiShotImageEditPo> editQueryWrapper = new LambdaQueryWrapper<>();
            editQueryWrapper.eq(AiShotImageEditPo::getTaskId, taskId)
                    .eq(AiShotImageEditPo::getDelFlag, 0);

            AiShotImageEditPo editRecord = aiShotImageEditMapper.selectOne(editQueryWrapper);
            if (editRecord == null) {
                log.warn("未找到分镜图片编辑记录: taskId={}", taskId);
                return;
            }

            Long shotId = editRecord.getShotId();

            // 2. 根据shotId查询对应的画布分镜记录
            LambdaQueryWrapper<AiCanvasShotPo> shotQueryWrapper = new LambdaQueryWrapper<>();
            shotQueryWrapper.eq(AiCanvasShotPo::getId, shotId)
                    .eq(AiCanvasShotPo::getDelFlag, 0);

            AiCanvasShotPo canvasShotPo = aiCanvasShotMapper.selectOne(shotQueryWrapper);
            if (canvasShotPo != null) {
                updateCanvasShotStatus(canvasShotPo.getId(), ShotStatus.FAILED.getValue());
                log.info("任务失败，画布分镜状态保持不变: canvasId={}, shotCode={}",
                        canvasShotPo.getCanvasId(), canvasShotPo.getCode());
            } else {
                log.warn("未找到对应的画布分镜记录: originalShotId={}", shotId);
            }

        } catch (Exception e) {
            log.error("更新业务表状态异常 - 任务失败: taskId={}, error={}", taskId, e.getMessage(), e);
        }
    }

    /**
     * 更新或创建画布图片记录
     */
    private void updateCanvasImage(AiCanvasShotPo canvasShotPo, String imageUrl, String prompt,String userId) {
        try {


            String image = ossUtils.uploadFile(imageUrl, OSS_PATH.replace("{env}", env)
                    .replace("{userId}", userId)
                    .replace("{type}", "image") + IdUtil.fastSimpleUUID() + ".png");

            // 查询是否已存在画布图片记录
            LambdaQueryWrapper<AiCanvasImagePo> imageQueryWrapper = new LambdaQueryWrapper<>();
            imageQueryWrapper.eq(AiCanvasImagePo::getCanvasId, canvasShotPo.getCanvasId())
                    .eq(AiCanvasImagePo::getShotCode, canvasShotPo.getCode())
                    .eq(AiCanvasImagePo::getDelFlag, 0);

            AiCanvasImagePo canvasImagePo = aiCanvasImageMapper.selectOne(imageQueryWrapper);

            if (canvasImagePo == null) {
                // 创建新的画布图片记录
                canvasImagePo = new AiCanvasImagePo();
                canvasImagePo.setCanvasId(canvasShotPo.getCanvasId());
                canvasImagePo.setShotCode(canvasShotPo.getCode());
                canvasImagePo.setCreateTime(new Date());
                canvasImagePo.setDelFlag(0);
            }

            // 更新图片信息
            canvasImagePo.setImageUrl(image); // 只保存文件名，不包含完整URL
            canvasImagePo.setImagePrompt(prompt);
            canvasImagePo.setImageStatus(ResourceStatus.SUCCESS.getValue());
            canvasImagePo.setUpdateTime(new Date());

            if (canvasImagePo.getId() == null) {
                // 新增
                int insertResult = aiCanvasImageMapper.insert(canvasImagePo);
                log.info("创建画布图片记录: canvasId={}, shotCode={}, result={}",
                        canvasShotPo.getCanvasId(), canvasShotPo.getCode(), insertResult > 0 ? "成功" : "失败");
            } else {
                // 更新
                int updateResult = aiCanvasImageMapper.updateById(canvasImagePo);
                log.info("更新画布图片记录: id={}, result={}",
                        canvasImagePo.getId(), updateResult > 0 ? "成功" : "失败");
            }

            AiCanvasMaterialPo materialPo = new AiCanvasMaterialPo();
            materialPo.setCanvasId(canvasShotPo.getCanvasId());
            materialPo.setMaterialType(1); // 1-图片
            materialPo.setMaterialSource(1); // 1-生成
            materialPo.setMaterialUrl(image);
//            materialPo.setGenerationRecordId(); // 使用taskId作为生成记录ID

            materialPo.setCreateTime(new Date());
            materialPo.setUpdateTime(new Date());
            materialPo.setDelFlag(0);

            canvasMaterialMapper.insert(materialPo);

        } catch (Exception e) {
            log.error("更新画布图片记录异常: canvasId={}, shotCode={}, error={}",
                    canvasShotPo.getCanvasId(), canvasShotPo.getCode(), e.getMessage(), e);
        }
    }

    /**
     * 更新画布分镜状态
     */
    private void updateCanvasShotStatus(Long canvasShotId, Integer status) {
        try {
            LambdaUpdateWrapper<AiCanvasShotPo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(AiCanvasShotPo::getId, canvasShotId)
                    .set(AiCanvasShotPo::getShotStatus, status)
                    .set(AiCanvasShotPo::getUpdateTime, new Date());

            int updateResult = aiCanvasShotMapper.update(null, updateWrapper);
            log.info("更新画布分镜状态: id={}, status={}, result={}",
                    canvasShotId, status, updateResult > 0 ? "成功" : "失败");

        } catch (Exception e) {
            log.error("更新画布分镜状态异常: id={}, status={}, error={}",
                    canvasShotId, status, e.getMessage(), e);
        }
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFileName(String url) {
        if (!StringUtils.hasText(url)) {
            return url;
        }

        // 如果URL包含路径分隔符，提取最后一部分作为文件名
        int lastSlashIndex = url.lastIndexOf('/');
        if (lastSlashIndex >= 0 && lastSlashIndex < url.length() - 1) {
            return url.substring(lastSlashIndex + 1);
        }

        return url;
    }

    /**
     * 构建详细的错误消息
     */
    private String buildErrorMessage(ComfyUICallbackEventData eventData) {
        StringBuilder errorMsg = new StringBuilder();

        // 基本错误信息
        if (StringUtils.hasText(eventData.getMsg())) {
            errorMsg.append("错误: ").append(eventData.getMsg());
        }

        // 详细错误信息
        String failureDetails = eventData.getFailureDetails();
        if (StringUtils.hasText(failureDetails)) {
            try {
                // 尝试解析失败详情
                FailureDetailsDto failureDto = JSON.parseObject(failureDetails, FailureDetailsDto.class);
                if (failureDto != null && StringUtils.hasText(failureDto.getFailedReason())) {
                    // 进一步解析失败原因
                    FailureReasonDto reasonDto = JSON.parseObject(failureDto.getFailedReason(), FailureReasonDto.class);
                    if (reasonDto != null) {
                        if (errorMsg.length() > 0) {
                            errorMsg.append(" | ");
                        }
                        errorMsg.append("节点: ").append(reasonDto.getNodeName());

                        if (StringUtils.hasText(reasonDto.getExceptionMessage())) {
                            errorMsg.append(", 异常: ").append(reasonDto.getExceptionMessage());
                        }
                    }
                }
            } catch (Exception e) {
                // 如果解析失败，直接使用原始错误信息
                if (errorMsg.length() > 0) {
                    errorMsg.append(" | ");
                }
                errorMsg.append("详情: ").append(failureDetails);
            }
        }

        return errorMsg.length() > 0 ? errorMsg.toString() : "未知错误";
    }

    /**
     * 失败详情DTO
     */
    @Data
    private static class FailureDetailsDto {
        @JsonProperty("failedReason")
        private String failedReason;
    }

    /**
     * 失败原因DTO
     */
    @Data
    private static class FailureReasonDto {
        @JsonProperty("node_name")
        private String nodeName;

        @JsonProperty("exception_message")
        private String exceptionMessage;

        @JsonProperty("exception_type")
        private String exceptionType;

        @JsonProperty("node_id")
        private String nodeId;
    }
}
