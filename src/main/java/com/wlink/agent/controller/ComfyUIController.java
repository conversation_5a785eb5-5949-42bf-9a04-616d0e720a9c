package com.wlink.agent.controller;

import com.alibaba.cola.dto.SingleResponse;
import com.wlink.agent.client.model.comfyui.ComfyUICallbackRequest;
import com.wlink.agent.client.model.comfyui.ComfyUICallbackResponse;
import com.wlink.agent.client.model.comfyui.ComfyUIRunRequest;
import com.wlink.agent.client.model.comfyui.ComfyUIRunResponse;
import com.wlink.agent.event.ComfyUICallbackEvent;
import com.wlink.agent.service.ComfyUIService;
import org.springframework.context.ApplicationEventPublisher;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * ComfyUI 控制器
 */
@Slf4j
@RestController
@RequestMapping("/agent/comfyui")
@Tag(name = "ComfyUI 接口")
@RequiredArgsConstructor
public class ComfyUIController {

    private final ApplicationEventPublisher eventPublisher;



    /**
     * ComfyUI 结果回调处理接口
     *
     * @param request 回调请求
     * @return 回调响应
     */
    @PostMapping("/callback")
    @Operation(summary = "ComfyUI 结果回调", description = "接收 ComfyUI 的任务结果回调")
    public ComfyUICallbackResponse handleCallback(@Valid @RequestBody ComfyUICallbackRequest request) {
        log.info("接收到 ComfyUI 回调: event={}, taskId={}", request.getEvent(), request.getTaskId());
        try {
            // 发布异步事件进行处理
            ComfyUICallbackEvent event = new ComfyUICallbackEvent(this, request);
            eventPublisher.publishEvent(event);

            log.info("ComfyUI 回调事件已发布: event={}, taskId={}", request.getEvent(), request.getTaskId());
            // 立即返回成功响应
            return ComfyUICallbackResponse.success();

        } catch (Exception e) {
            log.error("处理 ComfyUI 回调异常: event={}, taskId={}, error={}",
                    request.getEvent(), request.getTaskId(), e.getMessage(), e);
            return ComfyUICallbackResponse.failure("处理回调异常: " + e.getMessage());
        }
    }
}
