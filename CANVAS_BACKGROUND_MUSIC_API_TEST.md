# 画布背景音乐功能测试

## 测试场景

### 1. 设置画布背景音乐

```bash
# 设置背景音乐
curl -X POST "http://localhost:8080/agent/canvas/background-music/set" \
  -H "Content-Type: application/json" \
  -d '{
    "canvasId": 123456789,
    "audioUrl": "https://example.com/background.mp3",
    "audioName": "轻松背景音乐.mp3",
    "audioDuration": 180000,
    "startPlayTime": 0,
    "endPlayTime": 180000,
    "startTrackTime": 0,
    "volume": 80,
    "fadeInTime": 2000,
    "fadeOutTime": 2000,
    "isLoop": 1,
    "audioFormat": "mp3",
    "fileSize": 5242880,
    "audioSource": 1,
    "description": "轻松愉快的背景音乐"
  }'
```

**期望结果：** 返回背景音乐ID

### 2. 获取画布详情（包含背景音乐）

```bash
# 获取画布详情
curl -X GET "http://localhost:8080/agent/canvas/detail/123456789"
```

**期望结果：** 返回画布详情，包含 `backgroundMusic` 字段

### 3. 单独获取背景音乐

```bash
# 获取背景音乐
curl -X GET "http://localhost:8080/agent/canvas/background-music/123456789"
```

**期望结果：** 返回背景音乐详细信息

### 4. 更新背景音乐

```bash
# 更新背景音乐
curl -X PUT "http://localhost:8080/agent/canvas/background-music/123456789" \
  -H "Content-Type: application/json" \
  -d '{
    "canvasId": 123456789,
    "audioUrl": "https://example.com/new-background.mp3",
    "audioName": "新背景音乐.mp3",
    "audioDuration": 240000,
    "volume": 90,
    "isLoop": 0,
    "audioSource": 1,
    "description": "更新后的背景音乐"
  }'
```

**期望结果：** 更新成功

### 5. 通过空URL删除背景音乐

```bash
# 通过设置空URL删除背景音乐
curl -X POST "http://localhost:8080/agent/canvas/background-music/set" \
  -H "Content-Type: application/json" \
  -d '{
    "canvasId": 123456789,
    "audioUrl": null
  }'
```

**期望结果：** 背景音乐被删除

### 6. 验证删除结果

```bash
# 再次获取画布详情
curl -X GET "http://localhost:8080/agent/canvas/detail/123456789"
```

**期望结果：** `backgroundMusic` 字段为 null

### 7. 通过删除接口删除背景音乐

```bash
# 先重新设置背景音乐
curl -X POST "http://localhost:8080/agent/canvas/background-music/set" \
  -H "Content-Type: application/json" \
  -d '{
    "canvasId": 123456789,
    "audioUrl": "https://example.com/test.mp3",
    "audioName": "测试音乐.mp3",
    "audioDuration": 120000,
    "volume": 70
  }'

# 然后通过删除接口删除
curl -X DELETE "http://localhost:8080/agent/canvas/background-music/123456789"
```

**期望结果：** 背景音乐被删除

## 测试检查点

### 数据库检查

1. **设置背景音乐后**
   ```sql
   SELECT * FROM ai_canvas_background_music WHERE canvas_id = 123456789 AND del_flag = 0;
   ```
   应该返回一条记录

2. **删除背景音乐后**
   ```sql
   SELECT * FROM ai_canvas_background_music WHERE canvas_id = 123456789 AND del_flag = 0;
   ```
   应该返回空结果

3. **唯一约束测试**
   尝试为同一个画布设置两个背景音乐，应该只保留最新的一个

### API响应检查

1. **画布详情接口**
   - 有背景音乐时：`backgroundMusic` 字段包含完整信息
   - 无背景音乐时：`backgroundMusic` 字段为 null

2. **设置接口**
   - 新增时：返回新创建的背景音乐ID
   - 更新时：返回更新的背景音乐ID
   - 删除时（空URL）：返回被删除的背景音乐ID或null

3. **错误处理**
   - 画布不存在时：返回相应错误信息
   - 权限不足时：返回权限错误

## 性能测试

### 并发测试

```bash
# 并发设置背景音乐（应该只有一个成功）
for i in {1..5}; do
  curl -X POST "http://localhost:8080/agent/canvas/background-music/set" \
    -H "Content-Type: application/json" \
    -d "{
      \"canvasId\": 123456789,
      \"audioUrl\": \"https://example.com/test$i.mp3\",
      \"audioName\": \"测试音乐$i.mp3\",
      \"audioDuration\": 120000,
      \"volume\": 70
    }" &
done
wait
```

### 大数据量测试

```bash
# 为多个画布设置背景音乐
for i in {1..100}; do
  curl -X POST "http://localhost:8080/agent/canvas/background-music/set" \
    -H "Content-Type: application/json" \
    -d "{
      \"canvasId\": $((123456789 + i)),
      \"audioUrl\": \"https://example.com/test$i.mp3\",
      \"audioName\": \"测试音乐$i.mp3\",
      \"audioDuration\": 120000,
      \"volume\": 70
    }"
done
```

## 集成测试

### 画布删除测试

1. 为画布设置背景音乐
2. 删除画布
3. 检查背景音乐是否也被删除

```bash
# 设置背景音乐
curl -X POST "http://localhost:8080/agent/canvas/background-music/set" \
  -H "Content-Type: application/json" \
  -d '{
    "canvasId": 123456789,
    "audioUrl": "https://example.com/test.mp3",
    "audioDuration": 120000
  }'

# 删除画布
curl -X DELETE "http://localhost:8080/agent/canvas/123456789"

# 检查背景音乐是否被删除
curl -X GET "http://localhost:8080/agent/canvas/background-music/123456789"
```

**期望结果：** 背景音乐应该返回null或404

### 画布数据清理测试

1. 为画布设置背景音乐
2. 清理画布数据
3. 检查背景音乐是否被清理

## 边界测试

### 参数验证

1. **必填参数测试**
   ```bash
   # 缺少canvasId
   curl -X POST "http://localhost:8080/agent/canvas/background-music/set" \
     -H "Content-Type: application/json" \
     -d '{
       "audioUrl": "https://example.com/test.mp3"
     }'
   ```

2. **参数范围测试**
   ```bash
   # 音量超出范围
   curl -X POST "http://localhost:8080/agent/canvas/background-music/set" \
     -H "Content-Type: application/json" \
     -d '{
       "canvasId": 123456789,
       "audioUrl": "https://example.com/test.mp3",
       "volume": 150
     }'
   ```

3. **空值测试**
   ```bash
   # 空字符串URL
   curl -X POST "http://localhost:8080/agent/canvas/background-music/set" \
     -H "Content-Type: application/json" \
     -d '{
       "canvasId": 123456789,
       "audioUrl": ""
     }'
   ```

## 预期结果总结

1. ✅ 可以成功设置画布背景音乐
2. ✅ 画布详情接口返回背景音乐信息
3. ✅ 可以通过空URL删除背景音乐
4. ✅ 可以通过删除接口删除背景音乐
5. ✅ 一个画布只能有一个背景音乐
6. ✅ 删除画布时自动删除背景音乐
7. ✅ 参数验证正常工作
8. ✅ 并发操作安全
9. ✅ 性能表现良好
