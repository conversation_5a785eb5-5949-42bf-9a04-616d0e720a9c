//package com.wlink.agent.job;
//
//import cn.hutool.core.util.IdUtil;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//
//import com.wlink.agent.constant.RedisKeyConstant;
//import com.wlink.agent.dao.mapper.AgentInfoMapper;
//import com.wlink.agent.dao.mapper.AgentLiveRoomInfoMapper;
//import com.wlink.agent.dao.po.AgentInfoPo;
//import com.wlink.agent.dao.po.AgentLiveRoomInfoPo;
//import com.wlink.agent.enums.LiveStatusEnum;
//import com.wlink.agent.netty.DistributedRoomManager;
//import com.wlink.agent.proto.BaseMessage;
//import com.wlink.agent.proto.MessageType;
//import com.wlink.agent.proto.UpdateRoomInfo;
//import com.xxl.job.core.biz.model.ReturnT;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import io.jsonwebtoken.lang.Collections;
//import lombok.extern.slf4j.Slf4j;
//import org.redisson.api.RSet;
//import org.redisson.api.RedissonClient;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.Objects;
//
///**
// * 房间信息同步任务
// * 定时同步房间观看人数等信息给房间内所有用户
// */
//@Slf4j
//@Component
//public class RoomInfoSyncJob {
//
//    @Resource
//    private RedissonClient redissonClient;
//
//    @Resource
//    private DistributedRoomManager roomManager;
//
//    @Resource
//    AgentInfoMapper agentInfoMapper;
//
//    @Resource
//    private AgentLiveRoomInfoMapper agentLiveRoomInfoMapper;
//
//    /**
//     * 每10秒执行一次
//     */
//    @XxlJob(value = "roomInfoSyncJobHandler")
//    public ReturnT<String> execute() {
//        try {
//            // 1. 获取所有正在直播的房间
//            List<AgentLiveRoomInfoPo> agentInfoPos = agentLiveRoomInfoMapper.selectList(new LambdaQueryWrapper<AgentLiveRoomInfoPo>()
//                    .eq(AgentLiveRoomInfoPo::getLiveStatus, LiveStatusEnum.LIVING.getCode()));
//
//            if (Collections.isEmpty(agentInfoPos)) {
//                log.debug("没有正在直播的房间");
//                return ReturnT.SUCCESS;
//            }
//            // 2. 遍历每个房间，获取房间信息并同步
//            for (AgentLiveRoomInfoPo agentInfoPo : agentInfoPos) {
//                try {
//                    syncRoomInfo(agentInfoPo.getRoomId());
//                } catch (Exception e) {
//                    log.error("同步房间 {} 信息失败", agentInfoPo.getRoomId(), e);
//                }
//            }
//            log.debug("房间信息同步任务执行完成");
//            return ReturnT.SUCCESS;
//        } catch (Exception e) {
//            log.error("房间信息同步任务执行失败", e);
//            return ReturnT.FAIL;
//        }
//    }
//
//    /**
//     * 同步单个房间的信息
//     */
//    private void syncRoomInfo(String roomId) {
//        // 1. 获取房间用户集合
//        String roomUsersKey = RedisKeyConstant.getKey(RedisKeyConstant.LiveRoom.ROOM_USERS, roomId);
//        RSet<String> roomUsers = redissonClient.getSet(roomUsersKey);
//
//        // 2. 获取房间弹幕数量
//        String danmakuListKey = RedisKeyConstant.getKey(RedisKeyConstant.LiveRoom.DANMAKU_MSG_QUEUE, roomId);
//        long danmakuCount = redissonClient.getList(danmakuListKey).size();
//
//        // 3. 获取房间点赞数
////        String likeCountKey = RedisKeyConstant.getKey(RedisKeyConstant.LiveRoom.LIKE_COUNT, roomId);
////        RBucket<Long> likeBucket = redissonClient.getBucket(likeCountKey);
////        long likeCount = likeBucket.get() != null ? likeBucket.get() : 0;
//        if (!Objects.equals(roomUsers.size(), 0)){
//            // 4. 构建同步消息
//            UpdateRoomInfo syncMessage = UpdateRoomInfo.newBuilder()
//                    .setRoomId(roomId)
//                    .setUserCount(roomUsers.size())
//                    .setLikeCount(0)
//                    .build();
//
//            BaseMessage message = BaseMessage.newBuilder()
//                    .setType(MessageType.UPDATE_ROOM_INFO)
//                    .setMessageId(IdUtil.fastSimpleUUID())
//                    .setTimestamp(System.currentTimeMillis())
//                    .setUpdateRoomInfo(syncMessage)
//                    .build();
//
//            // 5. 广播给房间内所有用户
//            roomManager.broadcastToRoom(roomId, message);
//        }
//        log.debug("房间 {} 信息同步完成: 在线人数={}, 弹幕数={}, 点赞数={}",
//                roomId, roomUsers.size(), danmakuCount, 0);
//    }
//}