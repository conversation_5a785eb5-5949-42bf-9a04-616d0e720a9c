//package com.wlink.agent.job;
//
//import com.wlink.agent.utils.CollUtil;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.wlink.agent.constant.RedisKeyConstant;
//import com.wlink.agent.dao.dto.BarrageInfoDto;
//import com.wlink.agent.dao.dto.LiveMsgDto;
//import com.wlink.agent.dao.mapper.AgentInfoMapper;
//import com.wlink.agent.dao.mapper.AgentLiveRoomInfoMapper;
//import com.wlink.agent.dao.mapper.AgentLiveRoomRelMapper;
//import com.wlink.agent.dao.mapper.AgentLiveRoomTemplateMapper;
//import com.wlink.agent.dao.mapper.AgentSoundMapper;
//import com.wlink.agent.dao.po.AgentInfoPo;
//import com.wlink.agent.dao.po.AgentLiveRoomInfoPo;
//import com.wlink.agent.dao.po.AgentLiveRoomRelPo;
//import com.wlink.agent.dao.po.AgentLiveRoomTemplatePo;
//import com.wlink.agent.dao.po.AgentSoundPo;
//import com.wlink.agent.factory.StreamCallbackFactory;
//import com.wlink.agent.model.DelayTask;
//import com.wlink.agent.service.DelayQueueService;
//import com.wlink.agent.service.impl.DifyAiChatService;
//import com.wlink.agent.service.impl.OpenAIStreamingClient;
//import com.wlink.agent.service.impl.OpenAiStreamResponseCallback;
//import com.xxl.job.core.biz.model.ReturnT;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.redisson.api.RBlockingDeque;
//import org.redisson.api.RList;
//import org.redisson.api.RedissonClient;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.Objects;
//import java.util.stream.Collectors;
//
//
///**
// * <AUTHOR>
// * @version 1.0
// * @description:
// * @date 2025/1/6 13:57
// */
//@Slf4j
//@Component
//public class ProcessDelayQueueJob {
//
//
//    @Resource
//    private RedissonClient redissonClient;
//    @Resource
//    private OpenAIStreamingClient openAIStreamingClient;
//    @Resource
//    private DelayQueueService delayQueueService;
//    @Resource
//    DifyAiChatService difyAiChatService;
//    @Resource
//    StreamCallbackFactory streamCallbackFactory;
//    @Resource
//    AgentInfoMapper agentInfoMapper;
//    @Resource
//    AgentSoundMapper agentSoundMapper;
//
//    @Value("${live.chat.dify.key:app-uRbO9ixy6gQnGAkCXh0PpfZz}")
//    private String liveChatDifyKey;
//    @Autowired
//    private AgentLiveRoomInfoMapper agentLiveRoomInfoMapper;
//    @Autowired
//    private AgentLiveRoomRelMapper agentLiveRoomRelMapper;
//    @Autowired
//    private AgentLiveRoomTemplateMapper agentLiveRoomTemplateMapper;
//
//    @XxlJob(value = "processDelayQueueJob")
//    public ReturnT<String> processDelayQueueJob() {
//        try {
//            log.debug("Scanning delay queue at: {}", System.currentTimeMillis());
//            // 获取阻塞队列
//            RBlockingDeque<DelayTask> blockingDeque = redissonClient.getBlockingDeque(RedisKeyConstant.LiveRoom.DANMAKU_REPLY_QUEUE);
//            // 非阻塞方式获取任务
//            DelayTask task = blockingDeque.poll();
//
//            if (task != null) {
//                // 执行任务
//                executeTask(task);
//            }
//        } catch (Exception e) {
//            log.error("Error processing delay queue", e);
//            throw new RuntimeException(e);
//        }
//        return ReturnT.SUCCESS;
//    }
//
//    /**
//     * 执行具体任务
//     */
//    private void executeTask(DelayTask task) {
//        try {
//            switch (task.getType()) {
//                case "AI_REPLY":
//                    handleAiReply(task);
//                    break;
//                default:
//                    log.warn("Unknown task type: {}", task.getType());
//            }
//        } catch (Exception e) {
//            log.error("Failed to execute task: {}", task, e);
//        }
//    }
//
//    private void handleAiReply(DelayTask task) {
//        AgentLiveRoomInfoPo agentLiveRoomInfoPo = agentLiveRoomInfoMapper.selectOne(new LambdaQueryWrapper<AgentLiveRoomInfoPo>()
//                .eq(AgentLiveRoomInfoPo::getRoomId, task.getRoomId()));
//        if (agentLiveRoomInfoPo == null) {
//            log.error("AgentInfoPo is null, roomId: {}", task.getRoomId());
//            return;
//        }
//        AgentLiveRoomTemplatePo agentLiveRoomTemplatePo = agentLiveRoomTemplateMapper.selectById(agentLiveRoomInfoPo.getTemplateId());
//
//        if (Objects.equals(agentLiveRoomTemplatePo.getLiveType(), 1)) {
//            List<AgentLiveRoomRelPo> agentLiveRoomRelPos = agentLiveRoomRelMapper.selectList(new LambdaQueryWrapper<AgentLiveRoomRelPo>()
//                    .eq(AgentLiveRoomRelPo::getRoomId, task.getRoomId()));
//
//            if (CollUtil.isEmpty(agentLiveRoomRelPos)){
//                log.error("AgentLiveRoomRelPo is null, roomId: {}", task.getRoomId());
//            }
//            AgentInfoPo agentInfoPo = agentInfoMapper.selectById(agentLiveRoomRelPos.get(0).getAgentId());
//
//            JSONObject jsonObject = new JSONObject();
//            jsonObject.put("JoinState", "false");
//            jsonObject.put("CharName", agentInfoPo.getName());
//            jsonObject.put("Background", agentInfoPo.getBackgroundTidy());
//            jsonObject.put("SpeakStyle", agentInfoPo.getSpeakStyle());
//
//            AgentSoundPo agentSoundPo = agentSoundMapper.selectById(agentInfoPo.getVoiceId());
//            if (agentSoundPo == null) {
//                log.error("AgentSoundPo is null, roomId: {}", task.getRoomId());
//                return;
//            }
//            if (StringUtils.isNotBlank(task.getData())) {
//                OpenAiStreamResponseCallback callback = streamCallbackFactory.createCallback(task.getRoomId(), agentSoundPo);
//                // 处理AI回复任务
//                String key = RedisKeyConstant.getKey(RedisKeyConstant.Agent.CHAT_HISTORY, task.getRoomId());
//                difyAiChatService.chat(agentInfoPo.getUserName(),
//                        task.getRoomId(), agentLiveRoomTemplatePo.getDifyKey(), jsonObject, task.getData(), null, true, callback,true,key);
//            } else {
//                // 存储弹幕到Redis List
//                String danmakuListKey = RedisKeyConstant.getKey(RedisKeyConstant.LiveRoom.DANMAKU_MSG_QUEUE, task.getRoomId());
//                RList<BarrageInfoDto> danmakuList = redissonClient.getList(danmakuListKey);
//                List<BarrageInfoDto> danmakus = danmakuList.readAll();
//                if (CollUtil.isNotEmpty(danmakus)) {
//                    // 将弹幕列表转换为逗号分隔的字符串
//                    LiveMsgDto liveMsgDto = new LiveMsgDto();
//                    liveMsgDto.setDanmus(danmakus);
//                    // 处理AI回复任务
//                    OpenAiStreamResponseCallback callback = streamCallbackFactory.createCallback(task.getRoomId(), agentSoundPo);
//                    // 处理AI回复任务
//                    String key = RedisKeyConstant.getKey(RedisKeyConstant.Agent.CHAT_HISTORY, task.getRoomId());
//                    difyAiChatService.chat(agentInfoPo.getUserName(),
//                            task.getRoomId(), liveChatDifyKey, jsonObject, JSONObject.toJSONString(liveMsgDto), null, true, callback,true,key);
//                    danmakuList.clear();
//                } else {
//                    // 创建延迟任务
//                    DelayTask delayTask = DelayTask.builder()
//                            .type(DelayTask.TaskType.AI_REPLY)
//                            .roomId(task.getRoomId())
//                            .data(null)
//                            .createTime(System.currentTimeMillis())
//                            .build();
//                    // 添加到延迟队列
//                    delayQueueService.DelayQueueService(delayTask, 1000);
//                }
//            }
//        }
//    }
//
//}
