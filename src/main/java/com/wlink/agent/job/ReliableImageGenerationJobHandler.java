package com.wlink.agent.job;

import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.utils.ReliableDistributedSemaphore;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/5/26 11:05
 */
// 4. 增强的定时任务
@Slf4j
@Component
public class ReliableImageGenerationJobHandler {

    @Autowired
    private ReliableDistributedSemaphore semaphore;

    @Autowired
    private AiImageTaskQueueMapper taskMapper;

    /**
     * 清理超时任务和信号量泄漏
     */
    @XxlJob("cleanTimeoutAndLeakedSemaphore")
    public ReturnT<String> cleanTimeoutAndLeakedSemaphore(String param) {
        try {
            List<String> timeoutTasks = semaphore.checkAndCleanupTimeoutTasks();

            if (!timeoutTasks.isEmpty()) {
                log.info("清理了 {} 个超时或泄漏的信号量", timeoutTasks.size());
            }

            // 检查信号量状态一致性
            ReliableDistributedSemaphore.SemaphoreStatus status = semaphore.getDetailedStatus();
            if (!status.isDataConsistent()) {
                log.warn("发现信号量数据不一致，触发修复流程");
                semaphore.repairSemaphoreOnStartup();
            }

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("清理超时任务和信号量失败", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 信号量健康检查
     */
    @XxlJob("semaphoreHealthCheck")
    public ReturnT<String> semaphoreHealthCheck(String param) {
        try {
            ReliableDistributedSemaphore.SemaphoreStatus status = semaphore.getDetailedStatus();

            log.info("信号量健康检查 - 最大: {}, 已使用: {}, 可用: {}, 数据一致: {}",
                    status.getMaxPermits(), status.getUsedPermits(),
                    status.getAvailablePermits(), status.isDataConsistent());

            // 可以在这里添加告警逻辑
            if (status.getAvailablePermits() == 0) {
                log.warn("信号量已全部使用，请检查是否存在任务堵塞");
                // TODO: 发送告警通知
            }

            if (!status.isDataConsistent()) {
                log.error("信号量数据不一致，需要人工介入检查");
                // TODO: 发送告警通知
                
                // 添加严重程度检查，如果符合条件则触发自动修复尝试
                ReliableDistributedSemaphore.ForceResetCheck resetCheck = 
                    semaphore.isForceResetRequired(status);
                if (resetCheck.resetRequired()) {
                    log.warn("健康检查发现可能需要强制重置的信号量问题，等待专门的重置任务处理");
                }
            }

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("信号量健康检查失败", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 强制重置信号量（当检测到严重异常时）
     */
    @XxlJob("forceSemaphoreResetIfNeeded")
    public ReturnT<String> forceSemaphoreResetIfNeeded(String param) {
        try {
//            // 获取信号量当前状态
//            ReliableDistributedSemaphore.SemaphoreStatus status = semaphore.getDetailedStatus();
//            // 检查是否需要强制重置
//            ReliableDistributedSemaphore.ForceResetCheck resetCheck = semaphore.isForceResetRequired(status);
                // 记录严重异常，准备重置
//                log.info("检测到信号量严重异常，准备强制重置，原因: {}", resetCheck.reason());
                
                // 执行强制重置
                ReliableDistributedSemaphore.ResetResult result = 
                    semaphore.forceResetSemaphore("自动检测到严重异常: ");
                
                if (result.success()) {
                    log.info("信号量强制重置成功: {}", result.message());
                    return ReturnT.SUCCESS;
                } else {
                    log.error("信号量强制重置失败: {}", result.message());
                    return ReturnT.FAIL;
                }

        } catch (Exception e) {
            log.error("执行信号量强制重置检查失败", e);
            return ReturnT.FAIL;
        }
    }
}
