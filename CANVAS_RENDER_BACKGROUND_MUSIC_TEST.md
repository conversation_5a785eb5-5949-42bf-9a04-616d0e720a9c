# 画布渲染导出背景音乐功能测试

## 功能概述

在画布渲染导出数据中添加了背景音乐信息，使得Python渲染服务能够获取并处理画布的背景音乐。

## 实现细节

1. 在 `CanvasRenderDataDto` 中添加了 `backgroundMusic` 字段，类型为 `BackgroundMusicRenderDataDto`
2. 创建了 `BackgroundMusicRenderDataDto` 内部类，包含背景音乐的所有必要字段
3. 在 `VideoRenderExportServiceImpl` 中注入了 `CanvasBackgroundMusicService`
4. 在 `generateCanvasRenderData` 方法中添加了查询和设置背景音乐数据的逻辑
5. 添加了 `getBackgroundMusicData` 方法，用于将 `CanvasBackgroundMusicRes` 转换为 `BackgroundMusicRenderDataDto`

## 测试场景

### 1. 有背景音乐的画布渲染导出

**前置条件：**
- 创建一个画布
- 为画布设置背景音乐

**测试步骤：**
1. 调用画布渲染导出接口
2. 检查生成的JSON数据中是否包含背景音乐信息
3. 检查Python渲染服务是否正确处理了背景音乐

**期望结果：**
- 生成的JSON数据中包含完整的背景音乐信息
- Python渲染服务能够正确处理背景音乐
- 导出的视频包含背景音乐

### 2. 无背景音乐的画布渲染导出

**前置条件：**
- 创建一个画布
- 不设置背景音乐

**测试步骤：**
1. 调用画布渲染导出接口
2. 检查生成的JSON数据中背景音乐字段是否为null

**期望结果：**
- 生成的JSON数据中 `backgroundMusic` 字段为null
- Python渲染服务能够正确处理无背景音乐的情况
- 导出的视频不包含背景音乐

### 3. 背景音乐URL处理

**前置条件：**
- 创建一个画布
- 设置带有相对路径URL的背景音乐

**测试步骤：**
1. 调用画布渲染导出接口
2. 检查生成的JSON数据中背景音乐URL是否被正确处理

**期望结果：**
- 背景音乐URL应该通过 `MediaUrlPrefixUtil.getMediaUrl()` 方法处理为完整URL

## 测试数据

### 背景音乐数据示例

```json
{
  "audioUrl": "https://example.com/background.mp3",
  "audioName": "背景音乐.mp3",
  "audioDuration": 180000,
  "startPlayTime": 0,
  "endPlayTime": 180000,
  "startTrackTime": 0,
  "volume": 80,
  "fadeInTime": 2000,
  "fadeOutTime": 2000,
  "isLoop": 1,
  "audioFormat": "mp3",
  "audioSource": 1,
  "description": "轻松愉快的背景音乐"
}
```

### 渲染数据JSON示例

```json
{
  "canvasId": 123456789,
  "canvasName": "测试画布",
  "resolution": "1920x1080",
  "ratio": "16:9",
  "showSubtitle": 1,
  "fps": 24,
  "shots": [
    {
      "code": "SHOT_001",
      "type": "image",
      "sortOrder": 1,
      "composition": "中景",
      "displayType": "normal",
      "movement": "静止",
      "imageData": {
        "imageUrl": "https://example.com/image1.jpg",
        "imageAspectRatio": "16:9",
        "imageStatus": "success"
      },
      "audios": [
        {
          "audioUrl": "https://example.com/audio1.mp3",
          "audioType": 1,
          "text": "这是旁白文本",
          "voiceId": "voice1",
          "audioDuration": 5000,
          "sortOrder": 1
        }
      ]
    },
    {
      "code": "SHOT_002",
      "type": "video",
      "sortOrder": 2,
      "composition": "特写",
      "displayType": "normal",
      "movement": "平移",
      "videoData": {
        "videoUrl": "https://example.com/video1.mp4",
        "videoDuration": 10000,
        "videoAspectRatio": "16:9",
        "videoStatus": "success",
        "startFrameImage": "https://example.com/start_frame.jpg",
        "endFrameImage": "https://example.com/end_frame.jpg"
      },
      "audios": []
    }
  ],
  "backgroundMusic": {
    "audioUrl": "https://example.com/background.mp3",
    "audioName": "背景音乐.mp3",
    "audioDuration": 180000,
    "startPlayTime": 0,
    "endPlayTime": 180000,
    "startTrackTime": 0,
    "volume": 80,
    "fadeInTime": 2000,
    "fadeOutTime": 2000,
    "isLoop": 1,
    "audioFormat": "mp3",
    "audioSource": 1,
    "description": "轻松愉快的背景音乐"
  }
}
```

## 测试方法

### 单元测试

```java
@Test
public void testGenerateCanvasRenderDataWithBackgroundMusic() {
    // 准备测试数据
    Long canvasId = 123456789L;
    String resolution = "1920x1080";
    String ratio = "16:9";
    Integer showSubtitle = 1;
    Integer fps = 24;
    
    // 模拟背景音乐数据
    CanvasBackgroundMusicRes mockBackgroundMusic = new CanvasBackgroundMusicRes();
    mockBackgroundMusic.setId(987654321L);
    mockBackgroundMusic.setCanvasId(canvasId);
    mockBackgroundMusic.setAudioUrl("https://example.com/background.mp3");
    mockBackgroundMusic.setAudioName("背景音乐.mp3");
    mockBackgroundMusic.setAudioDuration(180000L);
    mockBackgroundMusic.setVolume(80);
    mockBackgroundMusic.setIsLoop(1);
    
    // 模拟服务返回
    when(canvasBackgroundMusicService.getCanvasBackgroundMusic(canvasId))
        .thenReturn(mockBackgroundMusic);
    
    // 调用测试方法
    CanvasRenderDataDto result = videoRenderExportService
        .generateCanvasRenderData(canvasId, resolution, ratio, showSubtitle, fps);
    
    // 验证结果
    assertNotNull(result.getBackgroundMusic());
    assertEquals("https://example.com/background.mp3", result.getBackgroundMusic().getAudioUrl());
    assertEquals("背景音乐.mp3", result.getBackgroundMusic().getAudioName());
    assertEquals(Long.valueOf(180000), result.getBackgroundMusic().getAudioDuration());
    assertEquals(Integer.valueOf(80), result.getBackgroundMusic().getVolume());
    assertEquals(Integer.valueOf(1), result.getBackgroundMusic().getIsLoop());
}

@Test
public void testGenerateCanvasRenderDataWithoutBackgroundMusic() {
    // 准备测试数据
    Long canvasId = 123456789L;
    String resolution = "1920x1080";
    String ratio = "16:9";
    Integer showSubtitle = 1;
    Integer fps = 24;
    
    // 模拟无背景音乐
    when(canvasBackgroundMusicService.getCanvasBackgroundMusic(canvasId))
        .thenReturn(null);
    
    // 调用测试方法
    CanvasRenderDataDto result = videoRenderExportService
        .generateCanvasRenderData(canvasId, resolution, ratio, showSubtitle, fps);
    
    // 验证结果
    assertNull(result.getBackgroundMusic());
}
```

### 集成测试

1. 设置背景音乐
```bash
curl -X POST "http://localhost:8080/agent/canvas/background-music/set" \
  -H "Content-Type: application/json" \
  -d '{
    "canvasId": 123456789,
    "audioUrl": "https://example.com/background.mp3",
    "audioName": "背景音乐.mp3",
    "audioDuration": 180000,
    "volume": 80,
    "isLoop": 1
  }'
```

2. 提交渲染导出任务
```bash
curl -X POST "http://localhost:8080/agent/video/render/export" \
  -H "Content-Type: application/json" \
  -d '{
    "canvasId": 123456789,
    "resolution": "1920x1080",
    "showSubtitle": 1,
    "fps": 24
  }'
```

3. 检查渲染任务记录中的JSON数据
```sql
SELECT canvas_data_json FROM ai_video_render_export WHERE canvas_id = 123456789 ORDER BY id DESC LIMIT 1;
```

## 注意事项

1. 确保背景音乐URL通过 `MediaUrlPrefixUtil.getMediaUrl()` 方法处理，以便Python渲染服务能够正确访问
2. 背景音乐可能为null，Python渲染服务需要处理这种情况
3. 背景音乐的音量范围为0-100，Python渲染服务可能需要将其转换为0-1的范围
4. 背景音乐的循环播放标志(isLoop)为0或1，Python渲染服务需要将其转换为布尔值
