package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 视频渲染导出响应
 */
@Schema(description = "视频渲染导出响应")
@Data
public class VideoRenderExportRes {

    @Schema(description = "渲染任务ID", example = "123456789")
    private Long id;

    @Schema(description = "画布ID", example = "987654321")
    private Long canvasId;

    @Schema(description = "分辨率", example = "1920x1080")
    private String resolution;

    @Schema(description = "比例", example = "16:9")
    private String ratio;

    @Schema(description = "是否显示字幕", example = "1", allowableValues = {"0", "1"})
    private Integer showSubtitle;

    @Schema(description = "渲染任务ID", example = "render_task_12345")
    private String renderTaskId;

    @Schema(description = "渲染状态", example = "1", allowableValues = {"0", "1", "2", "3"})
    private Integer status;

    @Schema(description = "状态描述", example = "渲染中")
    private String statusDesc;

    @Schema(description = "渲染后的视频地址", example = "https://example.com/video.mp4")
    private String videoUrl;

    @Schema(description = "视频时长(毫秒)", example = "60000")
    private Long videoDuration;

    //视频首帧
    @Schema(description = "视频首帧地址", example = "https://example.com/first_frame.jpg")
    private String firstFrameUrl;

    @Schema(description = "错误信息", example = "渲染失败原因")
    private String errorMessage;

    @Schema(description = "渲染开始时间", example = "2024-01-01T12:00:00Z")
    private Date startTime;

    @Schema(description = "渲染完成时间", example = "2024-01-01T12:05:00Z")
    private Date completeTime;

    @Schema(description = "创建时间", example = "2024-01-01T12:00:00Z")
    private Date createTime;

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) {
            return "未知状态";
        }
        return switch (status) {
            case 0 -> "排队中";
            case 1 -> "渲染中";
            case 2 -> "已完成";
            case 3 -> "失败";
            default -> "未知状态";
        };
    }
}
