package com.wlink.agent.service;

import com.wlink.agent.dao.po.AiCanvasShotPo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import com.wlink.agent.model.req.ShotCreateReq;
import com.wlink.agent.model.req.ShotEditReq;
import com.wlink.agent.model.req.ShotAudioCreateReq;
import com.wlink.agent.model.req.ShotAudioUpdateReq;
import com.wlink.agent.model.req.ShotAudioOrderUpdateReq;
import com.wlink.agent.model.req.ShotStatusBatchQueryReq;
import com.wlink.agent.model.req.VideoExtensionReq;
import com.wlink.agent.model.res.ShotStatusRes;
import com.wlink.agent.model.res.AiCanvasShotRes;

/**
* <AUTHOR>
* @description 针对表【ai_canvas_shot(画布分镜表)】的数据库操作Service
* @createDate 2025-06-23 17:39:35
*/
public interface AiCanvasShotService extends IService<AiCanvasShotPo> {

    /**
     * 添加空白分镜
     *
     * @param req 添加分镜请求
     * @return 新创建的分镜ID
     */
    Long createShot(ShotCreateReq req);

    /**
     * 编辑分镜信息
     *
     * @param req 编辑分镜请求
     */
    void editShot(ShotEditReq req);

    /**
     * 删除分镜
     *
     * @param shotId 分镜ID
     */
    void deleteShot(Long shotId);

    /**
     * 新增分镜音频
     *
     * @param req 音频新增请求
     * @return 新创建的音频ID
     */
    Long createShotAudio(ShotAudioCreateReq req);

    /**
     * 修改分镜音频
     *
     * @param req 音频修改请求
     */
    void updateShotAudio(ShotAudioUpdateReq req);

    /**
     * 删除分镜音频
     *
     * @param audioId 音频资源ID
     */
    void deleteShotAudio(Long audioId);

    /**
     * 调整分镜音频顺序
     *
     * @param req 音频顺序调整请求
     */
    void updateShotAudioOrder(ShotAudioOrderUpdateReq req);

    /**
     * 批量查询分镜状态
     *
     * @param req 批量查询请求
     * @return 分镜状态列表
     */
    List<ShotStatusRes> batchQueryShotStatus(ShotStatusBatchQueryReq req);

    /**
     * 根据分镜ID查询分镜详情
     *
     * @param shotId 分镜ID
     * @return 分镜详情
     */
    AiCanvasShotRes getShotDetail(Long shotId);

    /**
     * 视频延长功能
     * 在当前分镜后面增加一个分镜，并处理音频拆分
     *
     * @param req 视频延长请求
     * @return 新创建的分镜ID
     */
    Long extendVideo(VideoExtensionReq req);
}
