package com.wlink.agent.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.client.model.comfyui.ComfyUIRunRequest;
import com.wlink.agent.client.model.comfyui.ComfyUIRunResponse;
import com.alibaba.cola.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * ComfyUI API 客户端
 */
@Slf4j
@Component
public class ComfyUIApiClient {

    @Value("${comfyui.api.base-url:https://www.runninghub.cn}")
    private String baseUrl;

    @Value("${comfyui.api.timeout:60}")
    private int timeoutSeconds;

    private final ObjectMapper objectMapper;
    private final OkHttpClient httpClient;

    public ComfyUIApiClient() {
        this.objectMapper = new ObjectMapper();
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .build();
    }

    /**
     * 运行 ComfyUI 工作流
     *
     * @param request 运行请求
     * @return 运行响应
     */
    public ComfyUIRunResponse runWorkflow(ComfyUIRunRequest request) {
        log.info("开始调用 ComfyUI 运行工作流: webappId={}, nodeCount={}", 
                request.getWebappId(), 
                request.getNodeInfoList() != null ? request.getNodeInfoList().size() : 0);

        try {
            // 构建请求URL
            String url = baseUrl + "/task/openapi/ai-app/run";
            
            // 构建请求体
            String requestBody = objectMapper.writeValueAsString(request);
            log.debug("ComfyUI 请求体: {}", requestBody);

            // 构建HTTP请求
            Request httpRequest = new Request.Builder()
                    .url(url)
                    .post(RequestBody.create(requestBody, MediaType.get("application/json")))
                    .addHeader("Host", "www.runninghub.cn")
                    .addHeader("Content-Type", "application/json")
                    .build();

            // 发送请求
            try (Response response = httpClient.newCall(httpRequest).execute()) {
                String responseBody = response.body() != null ? response.body().string() : "";
                log.debug("ComfyUI 响应: status={}, body={}", response.code(), responseBody);

                if (!response.isSuccessful()) {
                    log.error("ComfyUI API 调用失败: status={}, body={}", response.code(), responseBody);
                    throw new BizException("ComfyUI API 调用失败: HTTP " + response.code());
                }

                // 解析响应
                ComfyUIRunResponse comfyResponse = objectMapper.readValue(responseBody, ComfyUIRunResponse.class);
                
                if (!comfyResponse.isSuccess()) {
                    log.error("ComfyUI 工作流运行失败: code={}, msg={}", 
                            comfyResponse.getCode(), comfyResponse.getMsg());
                    throw new BizException("ComfyUI 工作流运行失败: " + comfyResponse.getMsg());
                }

                log.info("ComfyUI 工作流运行成功: taskId={}, taskStatus={}", 
                        comfyResponse.getData().getTaskId(), 
                        comfyResponse.getData().getTaskStatus());

                return comfyResponse;
            }

        } catch (IOException e) {
            log.error("ComfyUI API 调用异常", e);
            throw new BizException("ComfyUI API 调用异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("ComfyUI 工作流运行异常", e);
            throw new BizException("ComfyUI 工作流运行异常: " + e.getMessage());
        }
    }

    /**
     * 运行 ComfyUI 工作流 - 使用指定的配置
     *
     * @param request 运行请求
     * @param customBaseUrl 自定义基础URL
     * @return 运行响应
     */
    public ComfyUIRunResponse runWorkflow(ComfyUIRunRequest request, String customBaseUrl) {
        String originalBaseUrl = this.baseUrl;
        try {
            this.baseUrl = customBaseUrl;
            return runWorkflow(request);
        } finally {
            this.baseUrl = originalBaseUrl;
        }
    }
}
