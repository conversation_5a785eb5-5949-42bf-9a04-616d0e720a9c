//package com.wlink.agent.job;
//
//import cn.hutool.core.util.IdUtil;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.wlink.agent.client.MinimaxFalClient;
//import com.wlink.agent.client.model.minimax.FalQueueStatus;
//import com.wlink.agent.client.model.minimax.MinimaxHailuo02ImageToVideoOutput;
//import com.wlink.agent.dao.mapper.AiCanvasMaterialMapper;
//import com.wlink.agent.dao.mapper.AiCanvasShotMapper;
//import com.wlink.agent.dao.mapper.AiCanvasVideoMapper;
//import com.wlink.agent.dao.mapper.AiVideoGenerationMapper;
//import com.wlink.agent.dao.po.AiCanvasMaterialPo;
//import com.wlink.agent.dao.po.AiCanvasShotPo;
//import com.wlink.agent.dao.po.AiCanvasVideoPo;
//import com.wlink.agent.dao.po.AiVideoGenerationPo;
//import com.wlink.agent.enums.ResourceStatus;
//import com.wlink.agent.enums.ShotStatus;
//import com.wlink.agent.enums.VideoGenerationStatus;
//import com.wlink.agent.utils.OssUtils;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import java.io.IOException;
//import java.util.Date;
//import java.util.List;
//import java.util.Map;
//
///**
// * MiniMax FAL 视频生成状态检查定时任务
// * 用于轮询 FAL API 的视频生成状态并处理完成的任务
// */
//@Slf4j
//@Component
//@RequiredArgsConstructor
//public class MinimaxFalVideoStatusCheckJob {
//
//    private final AiVideoGenerationMapper videoGenerationMapper;
//    private final AiCanvasShotMapper aiCanvasShotMapper;
//    private final AiCanvasVideoMapper aiCanvasVideoMapper;
//    private final AiCanvasMaterialMapper aiCanvasMaterialMapper;
//    private final MinimaxFalClient minimaxFalClient;
//    private final ObjectMapper objectMapper;
//    private final OssUtils ossUtils;
//
//    @Value("${spring.profiles.active}")
//    private String env;
//
//    private static final String OSS_PATH = "dify/{env}/{userId}/{type}/";
//    private static final String KEY_VIDEO_TASK_ID = "videoTaskId";
//    private static final String KEY_VIDEO_TYPE = "videoType";
//    private static final int MAX_FAILED_ATTEMPTS = 3;
//    private static final String FAILED_ATTEMPTS_KEY = "failedAttempts";
//
//    /**
//     * 检查 MiniMax FAL 视频生成状态
//     * 每分钟执行一次
//     */
//    @XxlJob("minimaxFalVideoStatusCheck")
//    public void checkVideoGenerationStatus() {
//        log.info("开始检查 MiniMax FAL 视频生成状态");
//
//        try {
//            // 查询所有处理中的 FAL 视频生成任务
//            List<AiVideoGenerationPo> processingTasks = getProcessingFalTasks();
//
//            if (processingTasks.isEmpty()) {
//                log.debug("没有需要检查的 MiniMax FAL 视频生成任务");
//                return;
//            }
//
//            log.info("找到 {} 个需要检查状态的 MiniMax FAL 视频生成任务", processingTasks.size());
//
//            for (AiVideoGenerationPo task : processingTasks) {
//                try {
//                    checkSingleTaskStatus(task);
//                } catch (Exception e) {
//                    log.error("检查任务状态失败: taskId={}", task.getId(), e);
//                    handleTaskCheckFailure(task);
//                }
//            }
//
//        } catch (Exception e) {
//            log.error("MiniMax FAL 视频生成状态检查任务执行失败", e);
//        }
//
//        log.info("MiniMax FAL 视频生成状态检查完成");
//    }
//
//    /**
//     * 获取所有处理中的 FAL 视频生成任务
//     */
//    private List<AiVideoGenerationPo> getProcessingFalTasks() {
//        return videoGenerationMapper.selectList(
//                new LambdaQueryWrapper<AiVideoGenerationPo>()
//                        .eq(AiVideoGenerationPo::getStatus, VideoGenerationStatus.PROCESSING.getValue())
//                        .like(AiVideoGenerationPo::getTaskInfo, "IMAGE_TO_VIDEO_FAL")
//                        .orderByAsc(AiVideoGenerationPo::getCreateTime)
//        );
//    }
//
//    /**
//     * 检查单个任务的状态
//     */
//    private void checkSingleTaskStatus(AiVideoGenerationPo task) {
//        log.debug("检查任务状态: taskId={}", task.getId());
//
//        try {
//            // 解析任务信息获取 request_id
//            Map<String, Object> taskInfo = objectMapper.readValue(task.getTaskInfo(), Map.class);
//            String requestId = (String) taskInfo.get(KEY_VIDEO_TASK_ID);
//            String videoType = (String) taskInfo.get(KEY_VIDEO_TYPE);
//
//            if (requestId == null || !"IMAGE_TO_VIDEO_FAL".equals(videoType)) {
//                log.warn("任务信息不完整或类型不匹配: taskId={}, requestId={}, videoType={}",
//                        task.getId(), requestId, videoType);
//                return;
//            }
//
//            // 查询 FAL API 状态
//            FalQueueStatus queueStatus = minimaxFalClient.checkQueueStatus(requestId);
//
//            log.debug("任务状态查询结果: taskId={}, requestId={}, status={}, queuePosition={}",
//                    task.getId(), requestId, queueStatus.getStatus(), queueStatus.getQueuePosition());
//
//            if (queueStatus.isCompleted()) {
//                // 任务完成，获取结果
//                handleTaskCompleted(task, requestId);
//            } else if (queueStatus.isRunning()) {
//                // 任务仍在运行中，更新状态信息
//                updateTaskProgress(task, queueStatus);
//            } else {
//                // 任务失败或未知状态
//                log.error("任务状态异常: taskId={}, requestId={}, status={}",
//                        task.getId(), requestId, queueStatus.getStatus());
//                handleTaskFailed(task, "任务状态异常: " + queueStatus.getStatus());
//            }
//
//        } catch (IOException e) {
//            log.error("查询任务状态失败: taskId={}", task.getId(), e);
//            handleTaskCheckFailure(task);
//        } catch (Exception e) {
//            log.error("处理任务状态检查异常: taskId={}", task.getId(), e);
//            handleTaskCheckFailure(task);
//        }
//    }
//
//    /**
//     * 处理任务完成
//     */
//    private void handleTaskCompleted(AiVideoGenerationPo task, String requestId) {
//        log.info("任务完成，开始获取结果: taskId={}, requestId={}", task.getId(), requestId);
//
//        try {
//            // 获取生成结果
//            MinimaxHailuo02ImageToVideoOutput result = minimaxFalClient.getResult(requestId);
//
//            if (result == null || !result.isValid()) {
//                log.error("获取任务结果失败: taskId={}, requestId={}", task.getId(), requestId);
//                handleTaskFailed(task, "获取任务结果失败");
//                return;
//            }
//
//            String videoUrl = result.getVideoUrl();
//            log.info("获取到视频URL: taskId={}, requestId={}, videoUrl={}",
//                    task.getId(), requestId, videoUrl);
//
//            // 上传视频到 OSS
//            String ossVideoUrl = uploadVideoToOss(task, videoUrl);
//
//            // 完成任务
//            completeTask(task, ossVideoUrl);
//
//            log.info("任务处理完成: taskId={}, requestId={}, ossVideoUrl={}",
//                    task.getId(), requestId, ossVideoUrl);
//
//        } catch (Exception e) {
//            log.error("处理任务完成失败: taskId={}, requestId={}", task.getId(), requestId, e);
//            handleTaskFailed(task, "处理任务完成失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 上传视频到 OSS
//     */
//    private String uploadVideoToOss(AiVideoGenerationPo task, String videoUrl) {
//        try {
//            String ossPath = OSS_PATH.replace("{env}", env)
//                    .replace("{userId}", task.getUserId())
//                    .replace("{type}", "video") + IdUtil.fastSimpleUUID() + ".mp4";
//
//            String ossVideoUrl = ossUtils.uploadFile(videoUrl, ossPath);
//            log.info("视频上传到 OSS 成功: taskId={}, originalUrl={}, ossUrl={}",
//                    task.getId(), videoUrl, ossVideoUrl);
//
//            return ossVideoUrl;
//
//        } catch (Exception e) {
//            log.error("上传视频到 OSS 失败: taskId={}, videoUrl={}", task.getId(), videoUrl, e);
//            throw new RuntimeException("上传视频到 OSS 失败", e);
//        }
//    }
//
//    /**
//     * 完成任务
//     */
//    private void completeTask(AiVideoGenerationPo task, String videoUrl) {
//        Date now = new Date();
//
//        // 1. 更新 AiVideoGenerationPo 状态
//        task.setStatus(VideoGenerationStatus.COMPLETED.getValue());
//        task.setVideoUrl(videoUrl);
//        task.setCompleteTime(now);
//        task.setUpdateTime(now);
//        videoGenerationMapper.updateById(task);
//
//        // 2. 更新 AiCanvasShotPo 状态
//        AiCanvasShotPo shotPo = aiCanvasShotMapper.selectById(task.getId());
//        if (shotPo != null) {
//            shotPo.setShotStatus(ShotStatus.COMPLETED.getValue());
//            shotPo.setUpdateTime(now);
//            aiCanvasShotMapper.updateById(shotPo);
//        }
//
//        // 3. 更新 AiCanvasVideoPo
//        updateCanvasVideo(task, videoUrl);
//
//        // 4. 插入 AiCanvasMaterialPo 记录
//        insertCanvasMaterial(task, videoUrl);
//
//        log.info("任务完成处理成功: taskId={}, videoUrl={}", task.getId(), videoUrl);
//    }
//
//    /**
//     * 更新画布视频记录
//     */
//    private void updateCanvasVideo(AiVideoGenerationPo task, String videoUrl) {
//        try {
//            // 根据任务ID查询对应的画布视频记录
//            AiCanvasVideoPo videoPo = aiCanvasVideoMapper.selectById(task.getId());
//            if (videoPo != null) {
//                videoPo.setVideoUrl(videoUrl);
//                videoPo.setVideoStatus(ResourceStatus.SUCCESS.getValue());
//                videoPo.setUpdateTime(new Date());
//                aiCanvasVideoMapper.updateById(videoPo);
//                log.debug("更新画布视频记录成功: taskId={}", task.getId());
//            }
//        } catch (Exception e) {
//            log.error("更新画布视频记录失败: taskId={}", task.getId(), e);
//        }
//    }
//
//    /**
//     * 插入画布材料记录
//     */
//    private void insertCanvasMaterial(AiVideoGenerationPo task, String videoUrl) {
//        try {
//            AiCanvasShotPo shotPo = aiCanvasShotMapper.selectById(task.getId());
//            if (shotPo != null) {
//                AiCanvasMaterialPo materialPo = AiCanvasMaterialPo.builder()
//                        .canvasId(shotPo.getCanvasId())
//                        .materialType("video")
//                        .materialUrl(videoUrl)
//                        .source(videoUrl)
//                        .generationRecordId(String.valueOf(task.getId()))
//                        .createTime(new Date())
//                        .updateTime(new Date())
//                        .delFlag(0)
//                        .build();
//
//                aiCanvasMaterialMapper.insert(materialPo);
//                log.debug("插入画布材料记录成功: taskId={}, materialId={}", task.getId(), materialPo.getId());
//            }
//        } catch (Exception e) {
//            log.error("插入画布材料记录失败: taskId={}", task.getId(), e);
//        }
//    }
//
//    /**
//     * 更新任务进度
//     */
//    private void updateTaskProgress(AiVideoGenerationPo task, FalQueueStatus queueStatus) {
//        // 可以在这里更新任务的进度信息，比如队列位置等
//        log.debug("任务仍在处理中: taskId={}, status={}, queuePosition={}",
//                task.getId(), queueStatus.getStatusDescription(), queueStatus.getQueuePositionDescription());
//    }
//
//    /**
//     * 处理任务失败
//     */
//    private void handleTaskFailed(AiVideoGenerationPo task, String errorMessage) {
//        Date now = new Date();
//
//        // 更新任务状态为失败
//        task.setStatus(VideoGenerationStatus.FAILED.getValue());
//        task.setErrorMessage(errorMessage);
//        task.setCompleteTime(now);
//        task.setUpdateTime(now);
//        videoGenerationMapper.updateById(task);
//
//        // 更新分镜状态为失败
//        AiCanvasShotPo shotPo = aiCanvasShotMapper.selectById(task.getId());
//        if (shotPo != null) {
//            shotPo.setShotStatus(ShotStatus.FAILED.getValue());
//            shotPo.setUpdateTime(now);
//            aiCanvasShotMapper.updateById(shotPo);
//        }
//
//        // 更新画布视频状态为失败
//        AiCanvasVideoPo videoPo = aiCanvasVideoMapper.selectById(task.getId());
//        if (videoPo != null) {
//            videoPo.setStatus(ResourceStatus.FAILED.getValue());
//            videoPo.setUpdateTime(now);
//            aiCanvasVideoMapper.updateById(videoPo);
//        }
//
//        log.error("任务处理失败: taskId={}, errorMessage={}", task.getId(), errorMessage);
//    }
//
//    /**
//     * 处理任务检查失败（网络错误等）
//     */
//    private void handleTaskCheckFailure(AiVideoGenerationPo task) {
//        try {
//            // 从 taskInfo 中获取失败次数
//            Map<String, Object> taskInfo = objectMapper.readValue(task.getTaskInfo(), Map.class);
//            int failedAttempts = taskInfo.containsKey(FAILED_ATTEMPTS_KEY) ?
//                    (Integer) taskInfo.get(FAILED_ATTEMPTS_KEY) + 1 : 1;
//
//            if (failedAttempts >= MAX_FAILED_ATTEMPTS) {
//                // 超过最大失败次数，标记任务为失败
//                handleTaskFailed(task, "任务状态检查失败次数超过限制: " + failedAttempts);
//            } else {
//                // 更新失败次数到 taskInfo
//                taskInfo.put(FAILED_ATTEMPTS_KEY, failedAttempts);
//                task.setTaskInfo(objectMapper.writeValueAsString(taskInfo));
//                task.setUpdateTime(new Date());
//                videoGenerationMapper.updateById(task);
//
//                log.warn("任务状态检查失败，将继续重试: taskId={}, failedAttempts={}",
//                        task.getId(), failedAttempts);
//            }
//        } catch (Exception e) {
//            log.error("处理任务检查失败时出错: taskId={}", task.getId(), e);
//            // 如果解析失败，直接标记任务为失败
//            handleTaskFailed(task, "处理任务检查失败时出错: " + e.getMessage());
//        }
//    }
//}
